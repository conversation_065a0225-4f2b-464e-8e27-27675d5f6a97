/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "containers_adList_v4_tsx";
exports.ids = ["containers_adList_v4_tsx"];
exports.modules = {

/***/ "./containers/adList/v4.module.scss":
/*!******************************************!*\
  !*** ./containers/adList/v4.module.scss ***!
  \******************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"grid\": \"v4_grid__kVTDA\",\n\t\"gridItem\": \"v4_gridItem__p8GQ2\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb250YWluZXJzL2FkTGlzdC92NC5tb2R1bGUuc2Nzcy5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZm9vZHltYW4vLi9jb250YWluZXJzL2FkTGlzdC92NC5tb2R1bGUuc2Nzcz83YjBjIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydHNcbm1vZHVsZS5leHBvcnRzID0ge1xuXHRcImdyaWRcIjogXCJ2NF9ncmlkX19rVlREQVwiLFxuXHRcImdyaWRJdGVtXCI6IFwidjRfZ3JpZEl0ZW1fX3A4R1EyXCJcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./containers/adList/v4.module.scss\n");

/***/ }),

/***/ "./containers/adList/v4.tsx":
/*!**********************************!*\
  !*** ./containers/adList/v4.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdList)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _v4_module_scss__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./v4.module.scss */ \"./containers/adList/v4.module.scss\");\n/* harmony import */ var _v4_module_scss__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_v4_module_scss__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @mui/material */ \"@mui/material\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_mui_material__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-i18next */ \"react-i18next\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_i18next__WEBPACK_IMPORTED_MODULE_4__]);\nreact_i18next__WEBPACK_IMPORTED_MODULE_4__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n/* eslint-disable @next/next/no-img-element */ \n\n\n\n\n\nfunction AdList({ data , loading  }) {\n    const { t  } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    if (!loading && data?.length === 0) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_v4_module_scss__WEBPACK_IMPORTED_MODULE_5___default().grid),\n            children: loading ? Array.from(Array(6).keys()).map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Skeleton, {\n                    variant: \"rectangular\",\n                    className: (_v4_module_scss__WEBPACK_IMPORTED_MODULE_5___default().gridItem)\n                }, item, false, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\adList\\\\v4.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 15\n                }, this)) : data?.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                    className: `${(_v4_module_scss__WEBPACK_IMPORTED_MODULE_5___default().gridItem)}`,\n                    href: `/ads/${item.id}`,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: item.img,\n                            alt: t(\"banner\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\adList\\\\v4.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 19\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\adList\\\\v4.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 17\n                    }, this)\n                }, item.id, false, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\adList\\\\v4.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 15\n                }, this))\n        }, void 0, false, {\n            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\adList\\\\v4.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\adList\\\\v4.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb250YWluZXJzL2FkTGlzdC92NC50c3guanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsNENBQTRDLEdBQzVDO0FBQTBCO0FBQ1M7QUFDTTtBQUVaO0FBQ2tCO0FBT2hDLFNBQVNLLE9BQU8sRUFBRUMsS0FBSSxFQUFFQyxRQUFPLEVBQVMsRUFBRTtJQUN2RCxNQUFNLEVBQUVDLEVBQUMsRUFBRSxHQUFHSiw2REFBY0E7SUFDNUIsSUFBSSxDQUFDRyxXQUFXRCxNQUFNRyxXQUFXLEdBQUcsT0FBTyxJQUFJO0lBQy9DLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVO2tCQUNiLDRFQUFDRDtZQUFJQyxXQUFXViw2REFBUTtzQkFDckJNLFVBQ0dNLE1BQU1DLElBQUksQ0FBQ0QsTUFBTSxHQUFHRSxJQUFJLElBQUlDLEdBQUcsQ0FBQyxDQUFDQyxxQkFDL0IsOERBQUNmLG1EQUFRQTtvQkFDUGdCLFNBQVE7b0JBQ1JQLFdBQVdWLGlFQUFZO21CQUNsQmdCOzs7OzRCQUdUWCxNQUFNVSxJQUFJLENBQUNDLHFCQUNULDhEQUFDZCxrREFBSUE7b0JBQ0hRLFdBQVcsQ0FBQyxFQUFFVixpRUFBWSxDQUFDLENBQUM7b0JBRTVCbUIsTUFBTSxDQUFDLEtBQUssRUFBRUgsS0FBS0ksRUFBRSxDQUFDLENBQUM7OEJBRXZCLDRFQUFDWDtrQ0FDQyw0RUFBQ1k7NEJBQUlDLEtBQUtOLEtBQUtLLEdBQUc7NEJBQUVFLEtBQUtoQixFQUFFOzs7Ozs7Ozs7OzttQkFKeEJTLEtBQUtJLEVBQUU7Ozs7eUJBT2Q7Ozs7Ozs7Ozs7O0FBSWQsQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL2Zvb2R5bWFuLy4vY29udGFpbmVycy9hZExpc3QvdjQudHN4P2Q0MzEiXSwic291cmNlc0NvbnRlbnQiOlsiLyogZXNsaW50LWRpc2FibGUgQG5leHQvbmV4dC9uby1pbWctZWxlbWVudCAqL1xuaW1wb3J0IFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IGNscyBmcm9tIFwiLi92NC5tb2R1bGUuc2Nzc1wiO1xuaW1wb3J0IHsgU2tlbGV0b24gfSBmcm9tIFwiQG11aS9tYXRlcmlhbFwiO1xuaW1wb3J0IHsgQmFubmVyIH0gZnJvbSBcImludGVyZmFjZXNcIjtcbmltcG9ydCBMaW5rIGZyb20gXCJuZXh0L2xpbmtcIjtcbmltcG9ydCB7IHVzZVRyYW5zbGF0aW9uIH0gZnJvbSBcInJlYWN0LWkxOG5leHRcIjtcblxudHlwZSBQcm9wcyA9IHtcbiAgZGF0YT86IEJhbm5lcltdO1xuICBsb2FkaW5nOiBib29sZWFuO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQWRMaXN0KHsgZGF0YSwgbG9hZGluZyB9OiBQcm9wcykge1xuICBjb25zdCB7IHQgfSA9IHVzZVRyYW5zbGF0aW9uKCk7XG4gIGlmICghbG9hZGluZyAmJiBkYXRhPy5sZW5ndGggPT09IDApIHJldHVybiBudWxsO1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT17Y2xzLmdyaWR9PlxuICAgICAgICB7bG9hZGluZ1xuICAgICAgICAgID8gQXJyYXkuZnJvbShBcnJheSg2KS5rZXlzKCkpLm1hcCgoaXRlbSkgPT4gKFxuICAgICAgICAgICAgICA8U2tlbGV0b25cbiAgICAgICAgICAgICAgICB2YXJpYW50PVwicmVjdGFuZ3VsYXJcIlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Y2xzLmdyaWRJdGVtfVxuICAgICAgICAgICAgICAgIGtleT17aXRlbX1cbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICkpXG4gICAgICAgICAgOiBkYXRhPy5tYXAoKGl0ZW0pID0+IChcbiAgICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2Ake2Nscy5ncmlkSXRlbX1gfVxuICAgICAgICAgICAgICAgIGtleT17aXRlbS5pZH1cbiAgICAgICAgICAgICAgICBocmVmPXtgL2Fkcy8ke2l0ZW0uaWR9YH1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8aW1nIHNyYz17aXRlbS5pbWd9IGFsdD17dChcImJhbm5lclwiKX0gLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgKSl9XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNscyIsIlNrZWxldG9uIiwiTGluayIsInVzZVRyYW5zbGF0aW9uIiwiQWRMaXN0IiwiZGF0YSIsImxvYWRpbmciLCJ0IiwibGVuZ3RoIiwiZGl2IiwiY2xhc3NOYW1lIiwiZ3JpZCIsIkFycmF5IiwiZnJvbSIsImtleXMiLCJtYXAiLCJpdGVtIiwidmFyaWFudCIsImdyaWRJdGVtIiwiaHJlZiIsImlkIiwiaW1nIiwic3JjIiwiYWx0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./containers/adList/v4.tsx\n");

/***/ })

};
;