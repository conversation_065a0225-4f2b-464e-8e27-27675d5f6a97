/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "components_confirmationModal_confirmationModal_tsx";
exports.ids = ["components_confirmationModal_confirmationModal_tsx"];
exports.modules = {

/***/ "./components/confirmationModal/confirmationModal.module.scss":
/*!********************************************************************!*\
  !*** ./components/confirmationModal/confirmationModal.module.scss ***!
  \********************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"wrapper\": \"confirmationModal_wrapper__NFPUR\",\n\t\"text\": \"confirmationModal_text__LXWur\",\n\t\"actions\": \"confirmationModal_actions__xeapU\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL2NvbmZpcm1hdGlvbk1vZGFsL2NvbmZpcm1hdGlvbk1vZGFsLm1vZHVsZS5zY3NzLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2Zvb2R5bWFuLy4vY29tcG9uZW50cy9jb25maXJtYXRpb25Nb2RhbC9jb25maXJtYXRpb25Nb2RhbC5tb2R1bGUuc2Nzcz8yMDg3Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydHNcbm1vZHVsZS5leHBvcnRzID0ge1xuXHRcIndyYXBwZXJcIjogXCJjb25maXJtYXRpb25Nb2RhbF93cmFwcGVyX19ORlBVUlwiLFxuXHRcInRleHRcIjogXCJjb25maXJtYXRpb25Nb2RhbF90ZXh0X19MWFd1clwiLFxuXHRcImFjdGlvbnNcIjogXCJjb25maXJtYXRpb25Nb2RhbF9hY3Rpb25zX194ZWFwVVwiXG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/confirmationModal/confirmationModal.module.scss\n");

/***/ }),

/***/ "./components/confirmationModal/confirmationModal.tsx":
/*!************************************************************!*\
  !*** ./components/confirmationModal/confirmationModal.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ConfirmationModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var containers_modal_modal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! containers/modal/modal */ \"./containers/modal/modal.tsx\");\n/* harmony import */ var _confirmationModal_module_scss__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./confirmationModal.module.scss */ \"./components/confirmationModal/confirmationModal.module.scss\");\n/* harmony import */ var _confirmationModal_module_scss__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_confirmationModal_module_scss__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-i18next */ \"react-i18next\");\n/* harmony import */ var components_button_secondaryButton__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! components/button/secondaryButton */ \"./components/button/secondaryButton.tsx\");\n/* harmony import */ var components_button_primaryButton__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! components/button/primaryButton */ \"./components/button/primaryButton.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_i18next__WEBPACK_IMPORTED_MODULE_3__]);\nreact_i18next__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n\nfunction ConfirmationModal({ open , handleClose , onSubmit , loading =false , title  }) {\n    const { t  } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(containers_modal_modal__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        open: open,\n        onClose: handleClose,\n        closable: false,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_confirmationModal_module_scss__WEBPACK_IMPORTED_MODULE_6___default().wrapper),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_confirmationModal_module_scss__WEBPACK_IMPORTED_MODULE_6___default().text),\n                    children: title\n                }, void 0, false, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\confirmationModal\\\\confirmationModal.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_confirmationModal_module_scss__WEBPACK_IMPORTED_MODULE_6___default().actions),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_button_secondaryButton__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            onClick: handleClose,\n                            children: t(\"no\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\confirmationModal\\\\confirmationModal.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_button_primaryButton__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            loading: loading,\n                            onClick: onSubmit,\n                            children: t(\"yes\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\confirmationModal\\\\confirmationModal.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\confirmationModal\\\\confirmationModal.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\confirmationModal\\\\confirmationModal.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\confirmationModal\\\\confirmationModal.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL2NvbmZpcm1hdGlvbk1vZGFsL2NvbmZpcm1hdGlvbk1vZGFsLnRzeC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7OztBQUFBO0FBQTBCO0FBQzBCO0FBQ0Y7QUFDSDtBQUNpQjtBQUNKO0FBVTdDLFNBQVNNLGtCQUFrQixFQUN4Q0MsS0FBSSxFQUNKQyxZQUFXLEVBQ1hDLFNBQVEsRUFDUkMsU0FBVSxLQUFLLEdBQ2ZDLE1BQUssRUFDQyxFQUFFO0lBQ1IsTUFBTSxFQUFFQyxFQUFDLEVBQUUsR0FBR1QsNkRBQWNBO0lBRTVCLHFCQUNFLDhEQUFDRiw4REFBY0E7UUFBQ00sTUFBTUE7UUFBTU0sU0FBU0w7UUFBYU0sVUFBVSxLQUFLO2tCQUMvRCw0RUFBQ0M7WUFBSUMsV0FBV2QsK0VBQVc7OzhCQUN6Qiw4REFBQ2E7b0JBQUlDLFdBQVdkLDRFQUFROzhCQUFHUzs7Ozs7OzhCQUMzQiw4REFBQ0k7b0JBQUlDLFdBQVdkLCtFQUFXOztzQ0FDekIsOERBQUNFLHlFQUFlQTs0QkFBQ2dCLFNBQVNaO3NDQUFjSSxFQUFFOzs7Ozs7c0NBQzFDLDhEQUFDUCx1RUFBYUE7NEJBQUNLLFNBQVNBOzRCQUFTVSxTQUFTWDtzQ0FDdkNHLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTWYsQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL2Zvb2R5bWFuLy4vY29tcG9uZW50cy9jb25maXJtYXRpb25Nb2RhbC9jb25maXJtYXRpb25Nb2RhbC50c3g/OWY3NyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgTW9kYWxDb250YWluZXIgZnJvbSBcImNvbnRhaW5lcnMvbW9kYWwvbW9kYWxcIjtcbmltcG9ydCBjbHMgZnJvbSBcIi4vY29uZmlybWF0aW9uTW9kYWwubW9kdWxlLnNjc3NcIjtcbmltcG9ydCB7IHVzZVRyYW5zbGF0aW9uIH0gZnJvbSBcInJlYWN0LWkxOG5leHRcIjtcbmltcG9ydCBTZWNvbmRhcnlCdXR0b24gZnJvbSBcImNvbXBvbmVudHMvYnV0dG9uL3NlY29uZGFyeUJ1dHRvblwiO1xuaW1wb3J0IFByaW1hcnlCdXR0b24gZnJvbSBcImNvbXBvbmVudHMvYnV0dG9uL3ByaW1hcnlCdXR0b25cIjtcblxudHlwZSBQcm9wcyA9IHtcbiAgb3BlbjogYm9vbGVhbjtcbiAgaGFuZGxlQ2xvc2U6ICgpID0+IHZvaWQ7XG4gIG9uU3VibWl0PzogKCkgPT4gdm9pZDtcbiAgbG9hZGluZz86IGJvb2xlYW47XG4gIHRpdGxlOiBzdHJpbmc7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBDb25maXJtYXRpb25Nb2RhbCh7XG4gIG9wZW4sXG4gIGhhbmRsZUNsb3NlLFxuICBvblN1Ym1pdCxcbiAgbG9hZGluZyA9IGZhbHNlLFxuICB0aXRsZSxcbn06IFByb3BzKSB7XG4gIGNvbnN0IHsgdCB9ID0gdXNlVHJhbnNsYXRpb24oKTtcblxuICByZXR1cm4gKFxuICAgIDxNb2RhbENvbnRhaW5lciBvcGVuPXtvcGVufSBvbkNsb3NlPXtoYW5kbGVDbG9zZX0gY2xvc2FibGU9e2ZhbHNlfT5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPXtjbHMud3JhcHBlcn0+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPXtjbHMudGV4dH0+e3RpdGxlfTwvZGl2PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT17Y2xzLmFjdGlvbnN9PlxuICAgICAgICAgIDxTZWNvbmRhcnlCdXR0b24gb25DbGljaz17aGFuZGxlQ2xvc2V9Pnt0KFwibm9cIil9PC9TZWNvbmRhcnlCdXR0b24+XG4gICAgICAgICAgPFByaW1hcnlCdXR0b24gbG9hZGluZz17bG9hZGluZ30gb25DbGljaz17b25TdWJtaXR9PlxuICAgICAgICAgICAge3QoXCJ5ZXNcIil9XG4gICAgICAgICAgPC9QcmltYXJ5QnV0dG9uPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvTW9kYWxDb250YWluZXI+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJNb2RhbENvbnRhaW5lciIsImNscyIsInVzZVRyYW5zbGF0aW9uIiwiU2Vjb25kYXJ5QnV0dG9uIiwiUHJpbWFyeUJ1dHRvbiIsIkNvbmZpcm1hdGlvbk1vZGFsIiwib3BlbiIsImhhbmRsZUNsb3NlIiwib25TdWJtaXQiLCJsb2FkaW5nIiwidGl0bGUiLCJ0Iiwib25DbG9zZSIsImNsb3NhYmxlIiwiZGl2IiwiY2xhc3NOYW1lIiwid3JhcHBlciIsInRleHQiLCJhY3Rpb25zIiwib25DbGljayJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./components/confirmationModal/confirmationModal.tsx\n");

/***/ })

};
;