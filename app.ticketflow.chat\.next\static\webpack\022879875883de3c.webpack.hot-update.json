{"c": ["webpack"], "r": ["pages/wallet", "pages/orders"], "m": ["./components/walletHistoryItem/walletHistoryItem.module.scss", "./components/walletHistoryItem/walletHistoryItem.tsx", "./containers/orderList/walletHistory.tsx", "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/walletHistoryItem/walletHistoryItem.module.scss", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5COSPanel%5Chome%5Capp.ticketflow.chat%5Cpages%5Cwallet.tsx&page=%2Fwallet!", "./pages/wallet.tsx", "./services/wallet.ts", "./components/orderListItem/orderListItem.module.scss", "./components/orderListItem/orderListItem.tsx", "./containers/orderList/orderList.tsx", "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/orderListItem/orderListItem.module.scss", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5COSPanel%5Chome%5Capp.ticketflow.chat%5Cpages%5Corders%5Cindex.tsx&page=%2Forders!", "./pages/orders/index.tsx"]}