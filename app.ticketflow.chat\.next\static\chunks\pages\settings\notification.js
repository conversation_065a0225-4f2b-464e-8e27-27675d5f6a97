/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/settings/notification"],{

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/loader/loading.module.scss":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/loader/loading.module.scss ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// Imports\nvar ___CSS_LOADER_API_IMPORT___ = __webpack_require__(/*! ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".loading_loading__hXLim {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  bottom: 0;\\n  width: 100%;\\n  height: 100%;\\n  z-index: 2;\\n  background-color: rgba(255, 255, 255, 0.4);\\n  transition: all 0.2s;\\n}\\n\\n.loading_pageLoading__0nn5j {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  bottom: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  z-index: 101;\\n  background-color: #fff;\\n  transition: all 0.2s;\\n}\\n\\n[data-theme=dark] .loading_loading__hXLim {\\n  background-color: rgba(20, 20, 20, 0.4);\\n}\\n[data-theme=dark] .loading_pageLoading__0nn5j {\\n  background-color: #141414;\\n}\", \"\",{\"version\":3,\"sources\":[\"webpack://components/loader/loading.module.scss\"],\"names\":[],\"mappings\":\"AAAA;EACE,aAAA;EACA,mBAAA;EACA,uBAAA;EACA,kBAAA;EACA,MAAA;EACA,OAAA;EACA,SAAA;EACA,WAAA;EACA,YAAA;EACA,UAAA;EACA,0CAAA;EACA,oBAAA;AACF;;AAEA;EACE,aAAA;EACA,mBAAA;EACA,uBAAA;EACA,eAAA;EACA,MAAA;EACA,OAAA;EACA,SAAA;EACA,YAAA;EACA,aAAA;EACA,YAAA;EACA,sBAAA;EACA,oBAAA;AACF;;AAGE;EACE,uCAAA;AAAJ;AAEE;EACE,yBAAA;AAAJ\",\"sourcesContent\":[\".loading {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  bottom: 0;\\n  width: 100%;\\n  height: 100%;\\n  z-index: 2;\\n  background-color: rgba($color: #fff, $alpha: 0.4);\\n  transition: all 0.2s;\\n}\\n\\n.pageLoading {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  bottom: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  z-index: 101;\\n  background-color: #fff;\\n  transition: all 0.2s;\\n}\\n\\n[data-theme=\\\"dark\\\"] {\\n  .loading {\\n    background-color: rgba($color: #141414, $alpha: 0.4);\\n  }\\n  .pageLoading {\\n    background-color: #141414;\\n  }\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n___CSS_LOADER_EXPORT___.locals = {\n\t\"loading\": \"loading_loading__hXLim\",\n\t\"pageLoading\": \"loading_pageLoading__0nn5j\"\n};\nmodule.exports = ___CSS_LOADER_EXPORT___;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/loader/loading.module.scss\n"));

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/notificationSettings/notificationSettings.module.scss":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/notificationSettings/notificationSettings.module.scss ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// Imports\nvar ___CSS_LOADER_API_IMPORT___ = __webpack_require__(/*! ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".notificationSettings_wrapper__FTVD4 {\\n  width: 100%;\\n}\\n.notificationSettings_wrapper__FTVD4 .notificationSettings_flex___ZZw_ {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 36px 0;\\n  border-bottom: 1px solid var(--grey);\\n}\\n@media (max-width: 576px) {\\n  .notificationSettings_wrapper__FTVD4 .notificationSettings_flex___ZZw_ {\\n    padding: 16px 0;\\n  }\\n}\\n.notificationSettings_wrapper__FTVD4 .notificationSettings_flex___ZZw_ .notificationSettings_text__341Ss {\\n  font-size: 18px;\\n  line-height: 18px;\\n  font-weight: 500;\\n  letter-spacing: -0.02em;\\n  color: var(--black);\\n}\\n@media (max-width: 576px) {\\n  .notificationSettings_wrapper__FTVD4 .notificationSettings_flex___ZZw_ .notificationSettings_text__341Ss {\\n    font-size: 16px;\\n    line-height: 18px;\\n  }\\n}\\n.notificationSettings_wrapper__FTVD4 .notificationSettings_flex___ZZw_ .notificationSettings_switch__KGPIH {\\n  display: flex;\\n  align-items: center;\\n  column-gap: 16px;\\n}\\n@media (max-width: 576px) {\\n  .notificationSettings_wrapper__FTVD4 .notificationSettings_flex___ZZw_ .notificationSettings_switch__KGPIH {\\n    column-gap: 14px;\\n  }\\n}\\n.notificationSettings_wrapper__FTVD4 .notificationSettings_flex___ZZw_ .notificationSettings_switch__KGPIH .notificationSettings_value__8hbDK {\\n  font-size: 14px;\\n  line-height: 18px;\\n  font-weight: 500;\\n  letter-spacing: -0.02em;\\n  color: var(--secondary-text);\\n}\", \"\",{\"version\":3,\"sources\":[\"webpack://containers/notificationSettings/notificationSettings.module.scss\"],\"names\":[],\"mappings\":\"AAAA;EACE,WAAA;AACF;AAAE;EACE,aAAA;EACA,mBAAA;EACA,8BAAA;EACA,eAAA;EACA,oCAAA;AAEJ;AADI;EANF;IAOI,eAAA;EAIJ;AACF;AAHI;EACE,eAAA;EACA,iBAAA;EACA,gBAAA;EACA,uBAAA;EACA,mBAAA;AAKN;AAJM;EANF;IAOI,eAAA;IACA,iBAAA;EAON;AACF;AALI;EACE,aAAA;EACA,mBAAA;EACA,gBAAA;AAON;AANM;EAJF;IAKI,gBAAA;EASN;AACF;AARM;EACE,eAAA;EACA,iBAAA;EACA,gBAAA;EACA,uBAAA;EACA,4BAAA;AAUR\",\"sourcesContent\":[\".wrapper {\\n  width: 100%;\\n  .flex {\\n    display: flex;\\n    align-items: center;\\n    justify-content: space-between;\\n    padding: 36px 0;\\n    border-bottom: 1px solid var(--grey);\\n    @media (max-width: 576px) {\\n      padding: 16px 0;\\n    }\\n    .text {\\n      font-size: 18px;\\n      line-height: 18px;\\n      font-weight: 500;\\n      letter-spacing: -0.02em;\\n      color: var(--black);\\n      @media (max-width: 576px) {\\n        font-size: 16px;\\n        line-height: 18px;\\n      }\\n    }\\n    .switch {\\n      display: flex;\\n      align-items: center;\\n      column-gap: 16px;\\n      @media (max-width: 576px) {\\n        column-gap: 14px;\\n      }\\n      .value {\\n        font-size: 14px;\\n        line-height: 18px;\\n        font-weight: 500;\\n        letter-spacing: -0.02em;\\n        color: var(--secondary-text);\\n      }\\n    }\\n  }\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n___CSS_LOADER_EXPORT___.locals = {\n\t\"wrapper\": \"notificationSettings_wrapper__FTVD4\",\n\t\"flex\": \"notificationSettings_flex___ZZw_\",\n\t\"text\": \"notificationSettings_text__341Ss\",\n\t\"switch\": \"notificationSettings_switch__KGPIH\",\n\t\"value\": \"notificationSettings_value__8hbDK\"\n};\nmodule.exports = ___CSS_LOADER_EXPORT___;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/notificationSettings/notificationSettings.module.scss\n"));

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/settings/settings.module.scss":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/settings/settings.module.scss ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// Imports\nvar ___CSS_LOADER_API_IMPORT___ = __webpack_require__(/*! ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".settings_root___nfYg {\\n  width: 100%;\\n  min-height: calc(100vh - 70px);\\n  background-color: var(--secondary-bg);\\n}\\n.settings_root___nfYg .settings_wrapper__GePvg {\\n  padding: 40px 0;\\n}\\n@media (max-width: 576px) {\\n  .settings_root___nfYg .settings_wrapper__GePvg {\\n    padding: 24px 0;\\n  }\\n}\\n.settings_root___nfYg .settings_wrapper__GePvg .settings_header__91cbk {\\n  margin-bottom: 30px;\\n}\\n@media (max-width: 576px) {\\n  .settings_root___nfYg .settings_wrapper__GePvg .settings_header__91cbk {\\n    margin-bottom: 24px;\\n  }\\n}\\n.settings_root___nfYg .settings_wrapper__GePvg .settings_header__91cbk .settings_title__0sGpi {\\n  margin: 0;\\n  font-size: 25px;\\n  line-height: 30px;\\n  font-weight: 600;\\n  letter-spacing: -0.04em;\\n  color: var(--dark-blue);\\n}\\n@media (max-width: 576px) {\\n  .settings_root___nfYg .settings_wrapper__GePvg .settings_header__91cbk .settings_title__0sGpi {\\n    font-size: 20px;\\n    line-height: 30px;\\n  }\\n}\\n.settings_root___nfYg .settings_wrapper__GePvg .settings_header__91cbk .settings_navigation__0IdWz {\\n  display: flex;\\n  align-items: center;\\n  column-gap: 50px;\\n  margin-top: 40px;\\n  border-bottom: 1px solid var(--grey);\\n}\\n@media (max-width: 576px) {\\n  .settings_root___nfYg .settings_wrapper__GePvg .settings_header__91cbk .settings_navigation__0IdWz {\\n    margin-top: 20px;\\n    column-gap: 40px;\\n  }\\n}\\n.settings_root___nfYg .settings_wrapper__GePvg .settings_header__91cbk .settings_navigation__0IdWz .settings_item__ky6Vg {\\n  position: relative;\\n  display: block;\\n  padding: 20px 0;\\n  font-size: 18px;\\n  line-height: 21px;\\n  color: var(--secondary-text);\\n  transition: all 0.2s;\\n}\\n@media (max-width: 576px) {\\n  .settings_root___nfYg .settings_wrapper__GePvg .settings_header__91cbk .settings_navigation__0IdWz .settings_item__ky6Vg {\\n    padding: 16px 0;\\n    font-size: 14px;\\n    line-height: 17px;\\n  }\\n}\\n.settings_root___nfYg .settings_wrapper__GePvg .settings_header__91cbk .settings_navigation__0IdWz .settings_item__ky6Vg.settings_disabled__pV_DC {\\n  pointer-events: none;\\n}\\n.settings_root___nfYg .settings_wrapper__GePvg .settings_header__91cbk .settings_navigation__0IdWz .settings_item__ky6Vg::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 3px;\\n  background-color: transparent;\\n  transition: all 0.2s;\\n}\\n.settings_root___nfYg .settings_wrapper__GePvg .settings_header__91cbk .settings_navigation__0IdWz .settings_item__ky6Vg:hover, .settings_root___nfYg .settings_wrapper__GePvg .settings_header__91cbk .settings_navigation__0IdWz .settings_item__ky6Vg.settings_active__TrFh7 {\\n  color: var(--dark-blue);\\n}\\n.settings_root___nfYg .settings_wrapper__GePvg .settings_header__91cbk .settings_navigation__0IdWz .settings_item__ky6Vg:hover::after, .settings_root___nfYg .settings_wrapper__GePvg .settings_header__91cbk .settings_navigation__0IdWz .settings_item__ky6Vg.settings_active__TrFh7::after {\\n  background-color: var(--black);\\n}\\n.settings_root___nfYg .settings_wrapper__GePvg .settings_main__rRnTv {\\n  width: 100%;\\n}\", \"\",{\"version\":3,\"sources\":[\"webpack://containers/settings/settings.module.scss\"],\"names\":[],\"mappings\":\"AAAA;EACE,WAAA;EACA,8BAAA;EACA,qCAAA;AACF;AAAE;EACE,eAAA;AAEJ;AADI;EAFF;IAGI,eAAA;EAIJ;AACF;AAHI;EACE,mBAAA;AAKN;AAJM;EAFF;IAGI,mBAAA;EAON;AACF;AANM;EACE,SAAA;EACA,eAAA;EACA,iBAAA;EACA,gBAAA;EACA,uBAAA;EACA,uBAAA;AAQR;AAPQ;EAPF;IAQI,eAAA;IACA,iBAAA;EAUR;AACF;AARM;EACE,aAAA;EACA,mBAAA;EACA,gBAAA;EACA,gBAAA;EACA,oCAAA;AAUR;AATQ;EANF;IAOI,gBAAA;IACA,gBAAA;EAYR;AACF;AAXQ;EACE,kBAAA;EACA,cAAA;EACA,eAAA;EACA,eAAA;EACA,iBAAA;EACA,4BAAA;EACA,oBAAA;AAaV;AAZU;EARF;IASI,eAAA;IACA,eAAA;IACA,iBAAA;EAeV;AACF;AAdU;EACE,oBAAA;AAgBZ;AAdU;EACE,WAAA;EACA,kBAAA;EACA,SAAA;EACA,OAAA;EACA,WAAA;EACA,WAAA;EACA,6BAAA;EACA,oBAAA;AAgBZ;AAdU;EAEE,uBAAA;AAeZ;AAdY;EACE,8BAAA;AAgBd;AAVI;EACE,WAAA;AAYN\",\"sourcesContent\":[\".root {\\n  width: 100%;\\n  min-height: calc(100vh - 70px);\\n  background-color: var(--secondary-bg);\\n  .wrapper {\\n    padding: 40px 0;\\n    @media (max-width: 576px) {\\n      padding: 24px 0;\\n    }\\n    .header {\\n      margin-bottom: 30px;\\n      @media (max-width: 576px) {\\n        margin-bottom: 24px;\\n      }\\n      .title {\\n        margin: 0;\\n        font-size: 25px;\\n        line-height: 30px;\\n        font-weight: 600;\\n        letter-spacing: -0.04em;\\n        color: var(--dark-blue);\\n        @media (max-width: 576px) {\\n          font-size: 20px;\\n          line-height: 30px;\\n        }\\n      }\\n      .navigation {\\n        display: flex;\\n        align-items: center;\\n        column-gap: 50px;\\n        margin-top: 40px;\\n        border-bottom: 1px solid var(--grey);\\n        @media (max-width: 576px) {\\n          margin-top: 20px;\\n          column-gap: 40px;\\n        }\\n        .item {\\n          position: relative;\\n          display: block;\\n          padding: 20px 0;\\n          font-size: 18px;\\n          line-height: 21px;\\n          color: var(--secondary-text);\\n          transition: all 0.2s;\\n          @media (max-width: 576px) {\\n            padding: 16px 0;\\n            font-size: 14px;\\n            line-height: 17px;\\n          }\\n          &.disabled {\\n            pointer-events: none;\\n          }\\n          &::after {\\n            content: \\\"\\\";\\n            position: absolute;\\n            bottom: 0;\\n            left: 0;\\n            width: 100%;\\n            height: 3px;\\n            background-color: transparent;\\n            transition: all 0.2s;\\n          }\\n          &:hover,\\n          &.active {\\n            color: var(--dark-blue);\\n            &::after {\\n              background-color: var(--black);\\n            }\\n          }\\n        }\\n      }\\n    }\\n    .main {\\n      width: 100%;\\n    }\\n  }\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n___CSS_LOADER_EXPORT___.locals = {\n\t\"root\": \"settings_root___nfYg\",\n\t\"wrapper\": \"settings_wrapper__GePvg\",\n\t\"header\": \"settings_header__91cbk\",\n\t\"title\": \"settings_title__0sGpi\",\n\t\"navigation\": \"settings_navigation__0IdWz\",\n\t\"item\": \"settings_item__ky6Vg\",\n\t\"disabled\": \"settings_disabled__pV_DC\",\n\t\"active\": \"settings_active__TrFh7\",\n\t\"main\": \"settings_main__rRnTv\"\n};\nmodule.exports = ___CSS_LOADER_EXPORT___;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/settings/settings.module.scss\n"));

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5COSPanel%5Chome%5Capp.ticketflow.chat%5Cpages%5Csettings%5Cnotification.tsx&page=%2Fsettings%2Fnotification!":
/*!**********************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5COSPanel%5Chome%5Capp.ticketflow.chat%5Cpages%5Csettings%5Cnotification.tsx&page=%2Fsettings%2Fnotification! ***!
  \**********************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/settings/notification\",\n      function () {\n        return __webpack_require__(/*! ./pages/settings/notification.tsx */ \"./pages/settings/notification.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/settings/notification\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWNsaWVudC1wYWdlcy1sb2FkZXIuanM/YWJzb2x1dGVQYWdlUGF0aD1DJTNBJTVDT1NQYW5lbCU1Q2hvbWUlNUNhcHAudGlja2V0Zmxvdy5jaGF0JTVDcGFnZXMlNUNzZXR0aW5ncyU1Q25vdGlmaWNhdGlvbi50c3gmcGFnZT0lMkZzZXR0aW5ncyUyRm5vdGlmaWNhdGlvbiEuanMiLCJtYXBwaW5ncyI6IjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsbUJBQU8sQ0FBQyw0RUFBbUM7QUFDMUQ7QUFDQTtBQUNBLE9BQU8sSUFBVTtBQUNqQixNQUFNLFVBQVU7QUFDaEI7QUFDQSxPQUFPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvPzI4Y2UiXSwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgKHdpbmRvdy5fX05FWFRfUCA9IHdpbmRvdy5fX05FWFRfUCB8fCBbXSkucHVzaChbXG4gICAgICBcIi9zZXR0aW5ncy9ub3RpZmljYXRpb25cIixcbiAgICAgIGZ1bmN0aW9uICgpIHtcbiAgICAgICAgcmV0dXJuIHJlcXVpcmUoXCIuL3BhZ2VzL3NldHRpbmdzL25vdGlmaWNhdGlvbi50c3hcIik7XG4gICAgICB9XG4gICAgXSk7XG4gICAgaWYobW9kdWxlLmhvdCkge1xuICAgICAgbW9kdWxlLmhvdC5kaXNwb3NlKGZ1bmN0aW9uICgpIHtcbiAgICAgICAgd2luZG93Ll9fTkVYVF9QLnB1c2goW1wiL3NldHRpbmdzL25vdGlmaWNhdGlvblwiXSlcbiAgICAgIH0pO1xuICAgIH1cbiAgIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5COSPanel%5Chome%5Capp.ticketflow.chat%5Cpages%5Csettings%5Cnotification.tsx&page=%2Fsettings%2Fnotification!\n"));

/***/ }),

/***/ "./components/loader/loading.module.scss":
/*!***********************************************!*\
  !*** ./components/loader/loading.module.scss ***!
  \***********************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var api = __webpack_require__(/*! !../../node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js */ \"./node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js\");\n            var content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./loading.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/loader/loading.module.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function(element) {\n                    // By default, style-loader injects CSS into the bottom\n                    // of <head>. This causes ordering problems between dev\n                    // and prod. To fix this, we render a <noscript> tag as\n                    // an anchor for the styles to be placed before. These\n                    // styles will be applied _before_ <style jsx global>.\n                    // These elements should always exist. If they do not,\n                    // this code should fail.\n                    var anchorElement = document.querySelector(\"#__next_css__DO_NOT_USE__\");\n                    var parentNode = anchorElement.parentNode// Normally <head>\n                    ;\n                    // Each style tag should be placed right before our\n                    // anchor. By inserting before and not after, we do not\n                    // need to track the last inserted element.\n                    parentNode.insertBefore(element, anchorElement);\n                };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\nif (true) {\n  if (!content.locals || module.hot.invalidate) {\n    var isEqualLocals = function isEqualLocals(a, b, isNamedExport) {\n    if (!a && b || a && !b) {\n        return false;\n    }\n    let p;\n    for(p in a){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (a[p] !== b[p]) {\n            return false;\n        }\n    }\n    for(p in b){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (!a[p]) {\n            return false;\n        }\n    }\n    return true;\n};\n    var oldLocals = content.locals;\n\n    module.hot.accept(\n      /*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./loading.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/loader/loading.module.scss\",\n      function () {\n        content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./loading.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/loader/loading.module.scss\");\n\n              content = content.__esModule ? content.default : content;\n\n              if (typeof content === 'string') {\n                content = [[module.id, content, '']];\n              }\n\n              if (!isEqualLocals(oldLocals, content.locals)) {\n                module.hot.invalidate();\n\n                return;\n              }\n\n              oldLocals = content.locals;\n\n              update(content);\n      }\n    )\n  }\n\n  module.hot.dispose(function() {\n    update();\n  });\n}\n\nmodule.exports = content.locals || {};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/loader/loading.module.scss\n"));

/***/ }),

/***/ "./containers/notificationSettings/notificationSettings.module.scss":
/*!**************************************************************************!*\
  !*** ./containers/notificationSettings/notificationSettings.module.scss ***!
  \**************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var api = __webpack_require__(/*! !../../node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js */ \"./node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js\");\n            var content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./notificationSettings.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/notificationSettings/notificationSettings.module.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function(element) {\n                    // By default, style-loader injects CSS into the bottom\n                    // of <head>. This causes ordering problems between dev\n                    // and prod. To fix this, we render a <noscript> tag as\n                    // an anchor for the styles to be placed before. These\n                    // styles will be applied _before_ <style jsx global>.\n                    // These elements should always exist. If they do not,\n                    // this code should fail.\n                    var anchorElement = document.querySelector(\"#__next_css__DO_NOT_USE__\");\n                    var parentNode = anchorElement.parentNode// Normally <head>\n                    ;\n                    // Each style tag should be placed right before our\n                    // anchor. By inserting before and not after, we do not\n                    // need to track the last inserted element.\n                    parentNode.insertBefore(element, anchorElement);\n                };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\nif (true) {\n  if (!content.locals || module.hot.invalidate) {\n    var isEqualLocals = function isEqualLocals(a, b, isNamedExport) {\n    if (!a && b || a && !b) {\n        return false;\n    }\n    let p;\n    for(p in a){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (a[p] !== b[p]) {\n            return false;\n        }\n    }\n    for(p in b){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (!a[p]) {\n            return false;\n        }\n    }\n    return true;\n};\n    var oldLocals = content.locals;\n\n    module.hot.accept(\n      /*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./notificationSettings.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/notificationSettings/notificationSettings.module.scss\",\n      function () {\n        content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./notificationSettings.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/notificationSettings/notificationSettings.module.scss\");\n\n              content = content.__esModule ? content.default : content;\n\n              if (typeof content === 'string') {\n                content = [[module.id, content, '']];\n              }\n\n              if (!isEqualLocals(oldLocals, content.locals)) {\n                module.hot.invalidate();\n\n                return;\n              }\n\n              oldLocals = content.locals;\n\n              update(content);\n      }\n    )\n  }\n\n  module.hot.dispose(function() {\n    update();\n  });\n}\n\nmodule.exports = content.locals || {};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./containers/notificationSettings/notificationSettings.module.scss\n"));

/***/ }),

/***/ "./containers/settings/settings.module.scss":
/*!**************************************************!*\
  !*** ./containers/settings/settings.module.scss ***!
  \**************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var api = __webpack_require__(/*! !../../node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js */ \"./node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js\");\n            var content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./settings.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/settings/settings.module.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function(element) {\n                    // By default, style-loader injects CSS into the bottom\n                    // of <head>. This causes ordering problems between dev\n                    // and prod. To fix this, we render a <noscript> tag as\n                    // an anchor for the styles to be placed before. These\n                    // styles will be applied _before_ <style jsx global>.\n                    // These elements should always exist. If they do not,\n                    // this code should fail.\n                    var anchorElement = document.querySelector(\"#__next_css__DO_NOT_USE__\");\n                    var parentNode = anchorElement.parentNode// Normally <head>\n                    ;\n                    // Each style tag should be placed right before our\n                    // anchor. By inserting before and not after, we do not\n                    // need to track the last inserted element.\n                    parentNode.insertBefore(element, anchorElement);\n                };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\nif (true) {\n  if (!content.locals || module.hot.invalidate) {\n    var isEqualLocals = function isEqualLocals(a, b, isNamedExport) {\n    if (!a && b || a && !b) {\n        return false;\n    }\n    let p;\n    for(p in a){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (a[p] !== b[p]) {\n            return false;\n        }\n    }\n    for(p in b){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (!a[p]) {\n            return false;\n        }\n    }\n    return true;\n};\n    var oldLocals = content.locals;\n\n    module.hot.accept(\n      /*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./settings.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/settings/settings.module.scss\",\n      function () {\n        content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./settings.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/settings/settings.module.scss\");\n\n              content = content.__esModule ? content.default : content;\n\n              if (typeof content === 'string') {\n                content = [[module.id, content, '']];\n              }\n\n              if (!isEqualLocals(oldLocals, content.locals)) {\n                module.hot.invalidate();\n\n                return;\n              }\n\n              oldLocals = content.locals;\n\n              update(content);\n      }\n    )\n  }\n\n  module.hot.dispose(function() {\n    update();\n  });\n}\n\nmodule.exports = content.locals || {};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./containers/settings/settings.module.scss\n"));

/***/ }),

/***/ "./components/inputs/switchInput.tsx":
/*!*******************************************!*\
  !*** ./components/inputs/switchInput.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SwitchInput; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @mui/material/styles */ \"./node_modules/@mui/material/styles/index.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mui/material */ \"./node_modules/@mui/material/index.js\");\n\n\n\n\nconst IOSSwitch = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_2__.styled)(_mui_material__WEBPACK_IMPORTED_MODULE_3__.Switch)({\n    width: 60,\n    height: 30,\n    padding: 0,\n    \"& .MuiSwitch-switchBase\": {\n        padding: 0,\n        margin: \"5px 7px\",\n        transitionDuration: \"300ms\",\n        \"&.Mui-checked\": {\n            transform: \"translateX(26px)\",\n            color: \"#fff\",\n            \"& + .MuiSwitch-track\": {\n                backgroundColor: \"#83EA00\",\n                opacity: 1,\n                border: \"0.8px solid #76D003 !important\"\n            },\n            \"&.Mui-disabled + .MuiSwitch-track\": {\n                opacity: 0.5\n            }\n        },\n        \"&.Mui-focusVisible .MuiSwitch-thumb\": {\n            color: \"#33cf4d\",\n            border: \"6px solid #fff\"\n        },\n        \"&.Mui-disabled .MuiSwitch-thumb\": {\n            color: \"#E7E7E7\"\n        },\n        \"&.Mui-disabled + .MuiSwitch-track\": {\n            opacity: 0.7\n        }\n    },\n    \"& .MuiSwitch-thumb\": {\n        boxSizing: \"border-box\",\n        position: \"relative\",\n        width: 20,\n        height: 20,\n        backgroundColor: \"var(--secondary-bg)\",\n        boxShadow: \"0px 2px 2px rgba(66, 113, 6, 0.4)\",\n        \"&::after\": {\n            content: \"''\",\n            position: \"absolute\",\n            top: \"50%\",\n            left: \"50%\",\n            transform: \"translate(-50%, -50%)\",\n            width: 2,\n            height: 6,\n            borderRadius: 100,\n            backgroundColor: \"var(--grey)\"\n        }\n    },\n    \"& .MuiSwitch-track\": {\n        borderRadius: 54,\n        backgroundColor: \"var(--border)\",\n        opacity: 1,\n        transition: \"background-color 0.5s\",\n        border: \"0 !important\"\n    }\n});\n_c = IOSSwitch;\nfunction SwitchInput(props) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IOSSwitch, {\n        ...props,\n        disableRipple: true\n    }, void 0, false, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\inputs\\\\switchInput.tsx\",\n        lineNumber: 65,\n        columnNumber: 10\n    }, this);\n}\n_c1 = SwitchInput;\nvar _c, _c1;\n$RefreshReg$(_c, \"IOSSwitch\");\n$RefreshReg$(_c1, \"SwitchInput\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/inputs/switchInput.tsx\n"));

/***/ }),

/***/ "./components/loader/loading.tsx":
/*!***************************************!*\
  !*** ./components/loader/loading.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Loading; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mui/material */ \"./node_modules/@mui/material/index.js\");\n/* harmony import */ var _loading_module_scss__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./loading.module.scss */ \"./components/loader/loading.module.scss\");\n/* harmony import */ var _loading_module_scss__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_loading_module_scss__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n\nfunction Loading(param) {\n    let {} = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_loading_module_scss__WEBPACK_IMPORTED_MODULE_2___default().loading),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_3__.CircularProgress, {}, void 0, false, {\n            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\loader\\\\loading.tsx\",\n            lineNumber: 10,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\loader\\\\loading.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n_c = Loading;\nvar _c;\n$RefreshReg$(_c, \"Loading\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL2xvYWRlci9sb2FkaW5nLnRzeC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFBO0FBQTBCO0FBQ3VCO0FBQ1Q7QUFJekIsU0FBU0csUUFBUSxLQUFTLEVBQUU7UUFBWCxFQUFTLEdBQVQ7SUFDOUIscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVdILHFFQUFXO2tCQUN6Qiw0RUFBQ0QsMkRBQWdCQTs7Ozs7Ozs7OztBQUd2QixDQUFDO0tBTnVCRSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9jb21wb25lbnRzL2xvYWRlci9sb2FkaW5nLnRzeD8yZWRkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IENpcmN1bGFyUHJvZ3Jlc3MgfSBmcm9tIFwiQG11aS9tYXRlcmlhbFwiO1xuaW1wb3J0IGNscyBmcm9tIFwiLi9sb2FkaW5nLm1vZHVsZS5zY3NzXCI7XG5cbnR5cGUgUHJvcHMgPSB7fTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTG9hZGluZyh7fTogUHJvcHMpIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT17Y2xzLmxvYWRpbmd9PlxuICAgICAgPENpcmN1bGFyUHJvZ3Jlc3MgLz5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkNpcmN1bGFyUHJvZ3Jlc3MiLCJjbHMiLCJMb2FkaW5nIiwiZGl2IiwiY2xhc3NOYW1lIiwibG9hZGluZyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./components/loader/loading.tsx\n"));

/***/ }),

/***/ "./components/seo.tsx":
/*!****************************!*\
  !*** ./components/seo.tsx ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SEO; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var constants_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! constants/constants */ \"./constants/constants.ts\");\n/* harmony import */ var constants_config__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! constants/config */ \"./constants/config.ts\");\n\n\n\n\n\nfunction SEO(param) {\n    let { title , description =constants_config__WEBPACK_IMPORTED_MODULE_4__.META_DESCRIPTION , image =constants_config__WEBPACK_IMPORTED_MODULE_4__.META_IMAGE , keywords =constants_config__WEBPACK_IMPORTED_MODULE_4__.META_KEYWORDS  } = param;\n    const currentURL = constants_constants__WEBPACK_IMPORTED_MODULE_3__.WEBSITE_URL;\n    const siteTitle = title ? title + \" | \" + constants_config__WEBPACK_IMPORTED_MODULE_4__.META_TITLE : constants_config__WEBPACK_IMPORTED_MODULE_4__.META_TITLE;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"viewport\",\n                content: \"width=device-width, initial-scale=1\"\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\seo.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                charSet: \"utf-8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\seo.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                children: siteTitle\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\seo.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"description\",\n                content: description\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\seo.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"keywords\",\n                content: keywords\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\seo.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                property: \"og:type\",\n                content: \"Website\"\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\seo.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"title\",\n                property: \"og:title\",\n                content: siteTitle\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\seo.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"description\",\n                property: \"og:description\",\n                content: description\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\seo.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"author\",\n                property: \"og:author\",\n                content: currentURL\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\seo.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                property: \"og:site_name\",\n                content: currentURL\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\seo.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"image\",\n                property: \"og:image\",\n                content: image\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\seo.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"twitter:card\",\n                content: \"summary\"\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\seo.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"twitter:title\",\n                content: siteTitle\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\seo.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"twitter:description\",\n                content: description\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\seo.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"twitter:site\",\n                content: currentURL\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\seo.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"twitter:creator\",\n                content: currentURL\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\seo.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"twitter:image\",\n                content: image\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\seo.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                rel: \"icon\",\n                href: \"/favicon.png\"\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\seo.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\seo.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n_c = SEO;\nvar _c;\n$RefreshReg$(_c, \"SEO\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/seo.tsx\n"));

/***/ }),

/***/ "./containers/notificationSettings/notificationSettings.tsx":
/*!******************************************************************!*\
  !*** ./containers/notificationSettings/notificationSettings.tsx ***!
  \******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ NotificationSettings; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @mui/material */ \"./node_modules/@mui/material/index.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _notificationSettings_module_scss__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./notificationSettings.module.scss */ \"./containers/notificationSettings/notificationSettings.module.scss\");\n/* harmony import */ var _notificationSettings_module_scss__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(_notificationSettings_module_scss__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var components_inputs_switchInput__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! components/inputs/switchInput */ \"./components/inputs/switchInput.tsx\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-query */ \"./node_modules/react-query/es/index.js\");\n/* harmony import */ var services_profile__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! services/profile */ \"./services/profile.ts\");\n/* harmony import */ var components_alert_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! components/alert/toast */ \"./components/alert/toast.tsx\");\n/* harmony import */ var hooks_useDidUpdate__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! hooks/useDidUpdate */ \"./hooks/useDidUpdate.tsx\");\n/* harmony import */ var contexts_auth_auth_context__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! contexts/auth/auth.context */ \"./contexts/auth/auth.context.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction NotificationSettings(param) {\n    let { data  } = param;\n    _s();\n    const { t  } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const { user  } = (0,contexts_auth_auth_context__WEBPACK_IMPORTED_MODULE_8__.useAuth)();\n    const [values, setValues] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultNotifications());\n    function defaultNotifications() {\n        var ref;\n        if (user === null || user === void 0 ? void 0 : (ref = user.notifications) === null || ref === void 0 ? void 0 : ref.length) {\n            var ref1;\n            return user === null || user === void 0 ? void 0 : (ref1 = user.notifications) === null || ref1 === void 0 ? void 0 : ref1.reduce((acc, cur)=>{\n                var ref;\n                return {\n                    ...acc,\n                    [cur.id]: Boolean(cur === null || cur === void 0 ? void 0 : (ref = cur.notification) === null || ref === void 0 ? void 0 : ref.active)\n                };\n            }, {});\n        }\n        return data.reduce((acc, cur)=>({\n                ...acc,\n                [cur.id]: false\n            }), {});\n    }\n    const { mutate  } = (0,react_query__WEBPACK_IMPORTED_MODULE_4__.useMutation)({\n        mutationFn: (data)=>services_profile__WEBPACK_IMPORTED_MODULE_5__[\"default\"].updateNotifications(data),\n        onError: ()=>{\n            (0,components_alert_toast__WEBPACK_IMPORTED_MODULE_6__.error)(t(\"try_again\"));\n        }\n    });\n    (0,hooks_useDidUpdate__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(()=>{\n        const notifications = Object.entries(values).map((item)=>({\n                notification_id: item[0],\n                active: Number(item[1])\n            }));\n        mutate({\n            notifications\n        });\n    }, [\n        values\n    ]);\n    const handleChange = (event)=>{\n        setValues({\n            ...values,\n            [event.target.name]: event.target.checked\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_notificationSettings_module_scss__WEBPACK_IMPORTED_MODULE_9___default().wrapper),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n            container: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n                item: true,\n                xs: 12,\n                md: 8,\n                children: data.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_notificationSettings_module_scss__WEBPACK_IMPORTED_MODULE_9___default().flex),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_notificationSettings_module_scss__WEBPACK_IMPORTED_MODULE_9___default().text),\n                                children: t(item.type)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\notificationSettings\\\\notificationSettings.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_notificationSettings_module_scss__WEBPACK_IMPORTED_MODULE_9___default()[\"switch\"]),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_inputs_switchInput__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        name: String(item.id),\n                                        checked: values[item.id],\n                                        onChange: handleChange\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\notificationSettings\\\\notificationSettings.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_notificationSettings_module_scss__WEBPACK_IMPORTED_MODULE_9___default().value),\n                                        children: values[item.id] ? t(\"on\") : t(\"off\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\notificationSettings\\\\notificationSettings.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\notificationSettings\\\\notificationSettings.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, item.id, true, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\notificationSettings\\\\notificationSettings.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\notificationSettings\\\\notificationSettings.tsx\",\n                lineNumber: 61,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\notificationSettings\\\\notificationSettings.tsx\",\n            lineNumber: 60,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\notificationSettings\\\\notificationSettings.tsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, this);\n}\n_s(NotificationSettings, \"EXlwh3+ktVIcji6uxIsVVthWVSw=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        contexts_auth_auth_context__WEBPACK_IMPORTED_MODULE_8__.useAuth,\n        react_query__WEBPACK_IMPORTED_MODULE_4__.useMutation,\n        hooks_useDidUpdate__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    ];\n});\n_c = NotificationSettings;\nvar _c;\n$RefreshReg$(_c, \"NotificationSettings\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./containers/notificationSettings/notificationSettings.tsx\n"));

/***/ }),

/***/ "./containers/settings/settings.tsx":
/*!******************************************!*\
  !*** ./containers/settings/settings.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SettingsContainer; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-i18next */ \"./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _settings_module_scss__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./settings.module.scss */ \"./containers/settings/settings.module.scss\");\n/* harmony import */ var _settings_module_scss__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_settings_module_scss__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var components_loader_loading__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! components/loader/loading */ \"./components/loader/loading.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction SettingsContainer(param) {\n    let { children , loading  } = param;\n    _s();\n    const { t  } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const { pathname  } = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: (_settings_module_scss__WEBPACK_IMPORTED_MODULE_6___default().root),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_settings_module_scss__WEBPACK_IMPORTED_MODULE_6___default().wrapper),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_settings_module_scss__WEBPACK_IMPORTED_MODULE_6___default().header),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: (_settings_module_scss__WEBPACK_IMPORTED_MODULE_6___default().title),\n                                children: t(\"settings\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\settings\\\\settings.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_settings_module_scss__WEBPACK_IMPORTED_MODULE_6___default().navigation),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/setting/payment\",\n                                        className: \"\".concat((_settings_module_scss__WEBPACK_IMPORTED_MODULE_6___default().item), \" \").concat(pathname.includes(\"payment\") ? (_settings_module_scss__WEBPACK_IMPORTED_MODULE_6___default().active) : \"\", \" \").concat((_settings_module_scss__WEBPACK_IMPORTED_MODULE_6___default().disabled)),\n                                        children: t(\"payment\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\settings\\\\settings.tsx\",\n                                        lineNumber: 24,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/settings/notification\",\n                                        className: \"\".concat((_settings_module_scss__WEBPACK_IMPORTED_MODULE_6___default().item), \" \").concat(pathname.includes(\"notification\") ? (_settings_module_scss__WEBPACK_IMPORTED_MODULE_6___default().active) : \"\"),\n                                        children: t(\"notification\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\settings\\\\settings.tsx\",\n                                        lineNumber: 32,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\settings\\\\settings.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\settings\\\\settings.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: (_settings_module_scss__WEBPACK_IMPORTED_MODULE_6___default().main),\n                        children: !loading ? children : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_loader_loading__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\settings\\\\settings.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 61\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\settings\\\\settings.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\settings\\\\settings.tsx\",\n                lineNumber: 20,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\settings\\\\settings.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\settings\\\\settings.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n_s(SettingsContainer, \"XL5gSdOLc/YgIPOW8zLqK5XdEtU=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation,\n        next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = SettingsContainer;\nvar _c;\n$RefreshReg$(_c, \"SettingsContainer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./containers/settings/settings.tsx\n"));

/***/ }),

/***/ "./pages/settings/notification.tsx":
/*!*****************************************!*\
  !*** ./pages/settings/notification.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Notification; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var components_seo__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! components/seo */ \"./components/seo.tsx\");\n/* harmony import */ var containers_settings_settings__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! containers/settings/settings */ \"./containers/settings/settings.tsx\");\n/* harmony import */ var containers_notificationSettings_notificationSettings__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! containers/notificationSettings/notificationSettings */ \"./containers/notificationSettings/notificationSettings.tsx\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-query */ \"./node_modules/react-query/es/index.js\");\n/* harmony import */ var services_profile__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! services/profile */ \"./services/profile.ts\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-i18next */ \"./node_modules/react-i18next/dist/es/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction Notification(param) {\n    let {} = param;\n    _s();\n    const { i18n  } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_7__.useTranslation)();\n    const locale = i18n.language;\n    const { data , isLoading  } = (0,react_query__WEBPACK_IMPORTED_MODULE_5__.useQuery)([\n        \"notifications\",\n        locale\n    ], ()=>services_profile__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getNotifications());\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_seo__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\pages\\\\settings\\\\notification.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(containers_settings_settings__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                loading: isLoading,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(containers_notificationSettings_notificationSettings__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    data: (data === null || data === void 0 ? void 0 : data.data) || []\n                }, void 0, false, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\pages\\\\settings\\\\notification.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\pages\\\\settings\\\\notification.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Notification, \"ilJypge8uTriI1v/ulWj9AD9wi0=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_7__.useTranslation,\n        react_query__WEBPACK_IMPORTED_MODULE_5__.useQuery\n    ];\n});\n_c = Notification;\nvar _c;\n$RefreshReg$(_c, \"Notification\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/settings/notification.tsx\n"));

/***/ }),

/***/ "./node_modules/next/head.js":
/*!***********************************!*\
  !*** ./node_modules/next/head.js ***!
  \***********************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("module.exports = __webpack_require__(/*! ./dist/shared/lib/head */ \"./node_modules/next/dist/shared/lib/head.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9oZWFkLmpzLmpzIiwibWFwcGluZ3MiOiJBQUFBLGlIQUFrRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9oZWFkLmpzPzg4NDkiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Rpc3Qvc2hhcmVkL2xpYi9oZWFkJylcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/head.js\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5COSPanel%5Chome%5Capp.ticketflow.chat%5Cpages%5Csettings%5Cnotification.tsx&page=%2Fsettings%2Fnotification!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);