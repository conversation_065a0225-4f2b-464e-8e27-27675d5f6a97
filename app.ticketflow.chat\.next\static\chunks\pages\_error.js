/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/_error"],{

/***/ "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=private-next-pages%2F_error&page=%2F_error!":
/*!***********************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=private-next-pages%2F_error&page=%2F_error! ***!
  \***********************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/_error\",\n      function () {\n        return __webpack_require__(/*! private-next-pages/_error */ \"./pages/_error.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/_error\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWNsaWVudC1wYWdlcy1sb2FkZXIuanM/YWJzb2x1dGVQYWdlUGF0aD1wcml2YXRlLW5leHQtcGFnZXMlMkZfZXJyb3ImcGFnZT0lMkZfZXJyb3IhLmpzIiwibWFwcGluZ3MiOiI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLG1CQUFPLENBQUMscURBQTJCO0FBQ2xEO0FBQ0E7QUFDQSxPQUFPLElBQVU7QUFDakIsTUFBTSxVQUFVO0FBQ2hCO0FBQ0EsT0FBTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLz84MDdiIl0sInNvdXJjZXNDb250ZW50IjpbIlxuICAgICh3aW5kb3cuX19ORVhUX1AgPSB3aW5kb3cuX19ORVhUX1AgfHwgW10pLnB1c2goW1xuICAgICAgXCIvX2Vycm9yXCIsXG4gICAgICBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHJldHVybiByZXF1aXJlKFwicHJpdmF0ZS1uZXh0LXBhZ2VzL19lcnJvclwiKTtcbiAgICAgIH1cbiAgICBdKTtcbiAgICBpZihtb2R1bGUuaG90KSB7XG4gICAgICBtb2R1bGUuaG90LmRpc3Bvc2UoZnVuY3Rpb24gKCkge1xuICAgICAgICB3aW5kb3cuX19ORVhUX1AucHVzaChbXCIvX2Vycm9yXCJdKVxuICAgICAgfSk7XG4gICAgfVxuICAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=private-next-pages%2F_error&page=%2F_error!\n"));

/***/ }),

/***/ "./pages/_error.tsx":
/*!**************************!*\
  !*** ./pages/_error.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"./node_modules/react-i18next/dist/es/index.js\");\n\nvar _s = $RefreshSig$();\n\n\nfunction Error(param) {\n    let { statusCode , hasGetInitialPropsRun , err  } = param;\n    _s();\n    const { t  } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    // Handle specific error cases\n    const getErrorMessage = ()=>{\n        var ref;\n        if (statusCode === 404) {\n            return t(\"page_not_found\") || \"P\\xe1gina n\\xe3o encontrada\";\n        }\n        if (statusCode === 500) {\n            return t(\"server_error\") || \"Erro interno do servidor\";\n        }\n        if (err === null || err === void 0 ? void 0 : (ref = err.message) === null || ref === void 0 ? void 0 : ref.includes(\"Loading chunk\")) {\n            return \"Erro ao carregar recursos. Recarregue a p\\xe1gina.\";\n        }\n        return t(\"something_went_wrong\") || \"Algo deu errado\";\n    };\n    const handleReload = ()=>{\n        if (true) {\n            window.location.reload();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\",\n            flexDirection: \"column\",\n            alignItems: \"center\",\n            justifyContent: \"center\",\n            minHeight: \"100vh\",\n            padding: \"20px\",\n            textAlign: \"center\",\n            fontFamily: \"system-ui, -apple-system, sans-serif\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                style: {\n                    fontSize: \"2rem\",\n                    marginBottom: \"1rem\",\n                    color: \"#333\"\n                },\n                children: statusCode ? \"Erro \".concat(statusCode) : \"Erro do Cliente\"\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\pages\\\\_error.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                style: {\n                    fontSize: \"1.1rem\",\n                    marginBottom: \"2rem\",\n                    color: \"#666\"\n                },\n                children: getErrorMessage()\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\pages\\\\_error.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: handleReload,\n                style: {\n                    padding: \"12px 24px\",\n                    fontSize: \"1rem\",\n                    backgroundColor: \"#007bff\",\n                    color: \"white\",\n                    border: \"none\",\n                    borderRadius: \"6px\",\n                    cursor: \"pointer\",\n                    transition: \"background-color 0.2s\"\n                },\n                onMouseOver: (e)=>e.currentTarget.style.backgroundColor = \"#0056b3\",\n                onMouseOut: (e)=>e.currentTarget.style.backgroundColor = \"#007bff\",\n                children: \"Recarregar P\\xe1gina\"\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\pages\\\\_error.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\pages\\\\_error.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n_s(Error, \"zlIdU9EjM2llFt74AbE2KsUJXyM=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation\n    ];\n});\n_c = Error;\nError.getInitialProps = (param)=>{\n    let { res , err  } = param;\n    const statusCode = res ? res.statusCode : err ? err.statusCode : 404;\n    return {\n        statusCode\n    };\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (Error);\nvar _c;\n$RefreshReg$(_c, \"Error\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_error.tsx\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=private-next-pages%2F_error&page=%2F_error!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);