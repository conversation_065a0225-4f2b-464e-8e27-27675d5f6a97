/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "containers_layout_footer_v2_tsx";
exports.ids = ["containers_layout_footer_v2_tsx"];
exports.modules = {

/***/ "./containers/layout/footer/v2.module.scss":
/*!*************************************************!*\
  !*** ./containers/layout/footer/v2.module.scss ***!
  \*************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"footer\": \"v2_footer__mZFnh\",\n\t\"appSection\": \"v2_appSection__4M_Pt\",\n\t\"item\": \"v2_item__I7J4P\",\n\t\"social\": \"v2_social__4WskJ\",\n\t\"socialItem\": \"v2_socialItem__Fd2ZH\",\n\t\"socialText\": \"v2_socialText__GHn6W\",\n\t\"main\": \"v2_main__q8iUk\",\n\t\"logoWrapper\": \"v2_logoWrapper__bsCp_\",\n\t\"phone\": \"v2_phone__IzHzr\",\n\t\"address\": \"v2_address__0xpWR\",\n\t\"column\": \"v2_column__lQFK_\",\n\t\"columnItem\": \"v2_columnItem__tsLjP\",\n\t\"listItem\": \"v2_listItem__zXEjS\",\n\t\"bottom\": \"v2_bottom__PiCC3\",\n\t\"text\": \"v2_text__T3rS2\",\n\t\"flex\": \"v2_flex___4cIk\",\n\t\"mutedLink\": \"v2_mutedLink__rmS3s\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb250YWluZXJzL2xheW91dC9mb290ZXIvdjIubW9kdWxlLnNjc3MuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2Zvb2R5bWFuLy4vY29udGFpbmVycy9sYXlvdXQvZm9vdGVyL3YyLm1vZHVsZS5zY3NzP2NiOTEiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0c1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cdFwiZm9vdGVyXCI6IFwidjJfZm9vdGVyX19tWkZuaFwiLFxuXHRcImFwcFNlY3Rpb25cIjogXCJ2Ml9hcHBTZWN0aW9uX180TV9QdFwiLFxuXHRcIml0ZW1cIjogXCJ2Ml9pdGVtX19JN0o0UFwiLFxuXHRcInNvY2lhbFwiOiBcInYyX3NvY2lhbF9fNFdza0pcIixcblx0XCJzb2NpYWxJdGVtXCI6IFwidjJfc29jaWFsSXRlbV9fRmQyWkhcIixcblx0XCJzb2NpYWxUZXh0XCI6IFwidjJfc29jaWFsVGV4dF9fR0huNldcIixcblx0XCJtYWluXCI6IFwidjJfbWFpbl9fcThpVWtcIixcblx0XCJsb2dvV3JhcHBlclwiOiBcInYyX2xvZ29XcmFwcGVyX19ic0NwX1wiLFxuXHRcInBob25lXCI6IFwidjJfcGhvbmVfX0l6SHpyXCIsXG5cdFwiYWRkcmVzc1wiOiBcInYyX2FkZHJlc3NfXzB4cFdSXCIsXG5cdFwiY29sdW1uXCI6IFwidjJfY29sdW1uX19sUUZLX1wiLFxuXHRcImNvbHVtbkl0ZW1cIjogXCJ2Ml9jb2x1bW5JdGVtX190c0xqUFwiLFxuXHRcImxpc3RJdGVtXCI6IFwidjJfbGlzdEl0ZW1fX3pYRWpTXCIsXG5cdFwiYm90dG9tXCI6IFwidjJfYm90dG9tX19QaUNDM1wiLFxuXHRcInRleHRcIjogXCJ2Ml90ZXh0X19UM3JTMlwiLFxuXHRcImZsZXhcIjogXCJ2Ml9mbGV4X19fNGNJa1wiLFxuXHRcIm11dGVkTGlua1wiOiBcInYyX211dGVkTGlua19fcm1TM3NcIlxufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./containers/layout/footer/v2.module.scss\n");

/***/ }),

/***/ "./containers/layout/footer/v2.tsx":
/*!*****************************************!*\
  !*** ./containers/layout/footer/v2.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _v2_module_scss__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./v2.module.scss */ \"./containers/layout/footer/v2.module.scss\");\n/* harmony import */ var _v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(_v2_module_scss__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @mui/material */ \"@mui/material\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_mui_material__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var components_icons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! components/icons */ \"./components/icons.tsx\");\n/* harmony import */ var contexts_theme_theme_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! contexts/theme/theme.context */ \"./contexts/theme/theme.context.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var constants_config__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! constants/config */ \"./constants/config.ts\");\n/* harmony import */ var remixicon_react_FacebookCircleFillIcon__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! remixicon-react/FacebookCircleFillIcon */ \"remixicon-react/FacebookCircleFillIcon\");\n/* harmony import */ var remixicon_react_FacebookCircleFillIcon__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_FacebookCircleFillIcon__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var remixicon_react_TwitterFillIcon__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! remixicon-react/TwitterFillIcon */ \"remixicon-react/TwitterFillIcon\");\n/* harmony import */ var remixicon_react_TwitterFillIcon__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_TwitterFillIcon__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var remixicon_react_InstagramLineIcon__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! remixicon-react/InstagramLineIcon */ \"remixicon-react/InstagramLineIcon\");\n/* harmony import */ var remixicon_react_InstagramLineIcon__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_InstagramLineIcon__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var contexts_settings_settings_context__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! contexts/settings/settings.context */ \"./contexts/settings/settings.context.tsx\");\n/* harmony import */ var hooks_useLocale__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! hooks/useLocale */ \"./hooks/useLocale.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([components_icons__WEBPACK_IMPORTED_MODULE_3__, hooks_useLocale__WEBPACK_IMPORTED_MODULE_11__]);\n([components_icons__WEBPACK_IMPORTED_MODULE_3__, hooks_useLocale__WEBPACK_IMPORTED_MODULE_11__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n/* eslint-disable @next/next/no-img-element */ \n\n\n\n\n\n\n\n\n\n\n\n\nfunction Footer({}) {\n    const { t  } = (0,hooks_useLocale__WEBPACK_IMPORTED_MODULE_11__[\"default\"])();\n    const { isDarkMode  } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(contexts_theme_theme_context__WEBPACK_IMPORTED_MODULE_4__.ThemeContext);\n    const isMobile = (0,_mui_material__WEBPACK_IMPORTED_MODULE_2__.useMediaQuery)(\"(max-width:576px)\");\n    const { settings  } = (0,contexts_settings_settings_context__WEBPACK_IMPORTED_MODULE_10__.useSettings)();\n    const isReferralActive = settings.referral_active == 1;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().footer),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {\n                    container: true,\n                    spacing: isMobile ? 4 : 6,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {\n                            item: true,\n                            xs: 12,\n                            md: 4,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().main),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().logoWrapper),\n                                        children: isDarkMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_icons__WEBPACK_IMPORTED_MODULE_3__.BrandLogoDark, {}, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                            lineNumber: 31,\n                                            columnNumber: 31\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_icons__WEBPACK_IMPORTED_MODULE_3__.BrandLogo, {}, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                            lineNumber: 31,\n                                            columnNumber: 51\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                        lineNumber: 30,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: `tel:${settings?.phone}`,\n                                        className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().phone),\n                                        children: settings?.phone\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                        lineNumber: 33,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().address),\n                                        children: settings?.address_text\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                        lineNumber: 36,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {\n                            item: true,\n                            xs: 12,\n                            md: 2,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().column),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().columnItem),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: \"/welcome\",\n                                            className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().listItem),\n                                            children: t(\"home.page\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                            lineNumber: 42,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().columnItem),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: \"/about\",\n                                            className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().listItem),\n                                            children: [\n                                                t(\"about\"),\n                                                \" \",\n                                                constants_config__WEBPACK_IMPORTED_MODULE_6__.META_TITLE\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 15\n                                    }, this),\n                                    isReferralActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().columnItem),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: \"/referrals\",\n                                            className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().listItem),\n                                            children: t(\"become.affiliate\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                            lineNumber: 53,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                        lineNumber: 52,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().columnItem),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: \"/careers\",\n                                            className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().listItem),\n                                            children: t(\"careers\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().columnItem),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: \"/blog\",\n                                            className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().listItem),\n                                            children: t(\"blog\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {\n                            item: true,\n                            xs: 12,\n                            md: 3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().column),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().columnItem),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: \"/recipes\",\n                                            className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().listItem),\n                                            children: t(\"recipes\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().columnItem),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: \"/help\",\n                                            className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().listItem),\n                                            children: t(\"get.helps\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().columnItem),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: \"/be-seller\",\n                                            className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().listItem),\n                                            children: t(\"add.your.restaurant\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().columnItem),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: \"/deliver\",\n                                            className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().listItem),\n                                            children: t(\"sign.up.to.deliver\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {\n                            item: true,\n                            xs: 12,\n                            md: 3,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().appSection),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: settings?.customer_app_ios,\n                                            className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().item),\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"/images/app-store.webp\",\n                                                alt: \"App store\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: settings?.customer_app_android,\n                                            className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().item),\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"/images/google-play.webp\",\n                                                alt: \"Google play\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().social),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: settings?.instagram_url,\n                                            className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().socialItem),\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((remixicon_react_InstagramLineIcon__WEBPACK_IMPORTED_MODULE_9___default()), {}, void 0, false, {\n                                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: settings?.twitter_url,\n                                            className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().socialItem),\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((remixicon_react_TwitterFillIcon__WEBPACK_IMPORTED_MODULE_8___default()), {}, void 0, false, {\n                                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: settings?.facebook_url,\n                                            className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().socialItem),\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((remixicon_react_FacebookCircleFillIcon__WEBPACK_IMPORTED_MODULE_7___default()), {}, void 0, false, {\n                                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().socialText),\n                                    children: t(\"follow.us\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().bottom),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {\n                        container: true,\n                        spacing: 4,\n                        flexDirection: isMobile ? \"column\" : \"row\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {\n                                item: true,\n                                xs: 12,\n                                md: 6,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().text),\n                                    children: [\n                                        \"\\xa9 \",\n                                        new Date().getFullYear(),\n                                        \" \",\n                                        settings?.footer_text\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 13\n                            }, this),\n                            !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {\n                                item: true,\n                                xs: 12,\n                                md: 3\n                            }, void 0, false, {\n                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 27\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {\n                                item: true,\n                                xs: 12,\n                                md: 3,\n                                alignSelf: isMobile ? \"flex-start\" : \"flex-end\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().flex),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: \"/privacy\",\n                                            className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().mutedLink),\n                                            children: t(\"privacy.policy\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: \"/terms\",\n                                            className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().mutedLink),\n                                            children: t(\"terms\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./containers/layout/footer/v2.tsx\n");

/***/ })

};
;