[{"C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\404.tsx": "1", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\about.tsx": "2", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\ads\\index.tsx": "3", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\ads\\[id].tsx": "4", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\api\\hello.ts": "5", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\be-seller.tsx": "6", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\blog\\index.tsx": "7", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\blog\\[id].tsx": "8", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\brands.tsx": "9", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\careers\\index.tsx": "10", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\careers\\[id].tsx": "11", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\deliver.tsx": "12", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\group\\[id].tsx": "13", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\help.tsx": "14", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\index.tsx": "15", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\liked.tsx": "16", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\login.tsx": "17", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\order-refunds.tsx": "18", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\orders\\index.tsx": "19", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\orders\\[id].tsx": "20", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\parcel-checkout.tsx": "21", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\parcels\\index.tsx": "22", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\parcels\\[id].tsx": "23", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\privacy.tsx": "24", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\profile.tsx": "25", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\promotion\\index.tsx": "26", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\promotion\\[id].tsx": "27", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\recipes\\index.tsx": "28", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\recipes\\[id].tsx": "29", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\referral-terms.tsx": "30", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\referrals.tsx": "31", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\register.tsx": "32", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\reservations\\index.tsx": "33", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\reservations\\[id].tsx": "34", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\reset-password.tsx": "35", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\restaurant\\[id]\\checkout.tsx": "36", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\restaurant\\[id]\\index.tsx": "37", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\saved-locations.tsx": "38", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\settings\\notification.tsx": "39", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\shop\\index.tsx": "40", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\shop\\[id].tsx": "41", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\shop-category\\index.tsx": "42", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\shop-category\\[id].tsx": "43", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\terms.tsx": "44", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\update-details.tsx": "45", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\update-password.tsx": "46", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\verify-phone.tsx": "47", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\wallet.tsx": "48", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\welcome.tsx": "49", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\_app.tsx": "50", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\_document.tsx": "51", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\addressModal\\addressCard.tsx": "52", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\addressModal\\addressModal.tsx": "53", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\addressModal\\deliveryAddressModal.tsx": "54", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\addressPopover\\addressPopover.tsx": "55", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\addressTypeSelector\\addressTypeSelector.tsx": "56", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\adSingle\\v2.tsx": "57", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\adSingle\\v3.tsx": "58", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\alert\\alert.tsx": "59", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\alert\\toast.tsx": "60", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\appDrawer\\appDrawer.tsx": "61", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\appDrawer\\mobileAppDrawer.tsx": "62", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\autoRepeatOrder\\autoRepeatOrder.tsx": "63", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\avatar.tsx": "64", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\badge\\badge.tsx": "65", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\badge\\v4.tsx": "66", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\bannerHeader\\bannerHeader.tsx": "67", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\bannerSingle\\bannerSingle.tsx": "68", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\bannerSingle\\v2.tsx": "69", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\beSellerModal\\beSellerModal.tsx": "70", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\bonusCaption\\bonusCaption.tsx": "71", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\booking\\booking.tsx": "72", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\branchList\\branchList.tsx": "73", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\brandShopCard\\v1.tsx": "74", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\brandShopCard\\v4.tsx": "75", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\button\\darkButton.tsx": "76", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\button\\primaryButton.tsx": "77", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\button\\secondaryButton.tsx": "78", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\carouselArrows\\carouselArrows.tsx": "79", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\cartButton\\cartButton.tsx": "80", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\cartButton\\protectedCartButton.tsx": "81", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\cartHeader\\cartHeader.tsx": "82", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\cartHeader\\memberCartHeader.tsx": "83", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\cartHeader\\protectedCartHeader.tsx": "84", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\cartProduct\\cartProduct.tsx": "85", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\cartProduct\\cartProductUI.tsx": "86", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\cartProduct\\memberCartProduct.tsx": "87", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\cartProduct\\protectedCartProduct.tsx": "88", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\cartServices\\cartServices.tsx": "89", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\cartTotal\\cartTotal.tsx": "90", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\cartTotal\\memberCartTotal.tsx": "91", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\categoryCard\\v1.tsx": "92", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\categoryCard\\v4.tsx": "93", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\categoryDropdown\\categoryDropdown.tsx": "94", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\categorySearchInput\\categorySearchInput.tsx": "95", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\changeAmountInput\\changeAmountInput.tsx": "96", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\chat\\adminMessage.tsx": "97", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\chat\\channel.tsx": "98", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\chat\\chat.tsx": "99", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\chat\\chatDate.tsx": "100", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\chat\\rippleButton.tsx": "101", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\chat\\uploadMedia.tsx": "102", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\chat\\userMessage.tsx": "103", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\checkoutProductItem\\checkoutProductItem.tsx": "104", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\clearCartModal\\cartReplacePrompt.tsx": "105", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\clearCartModal\\clearCartModal.tsx": "106", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\commentCard\\commentCard.tsx": "107", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\confirmationModal\\confirmationModal.tsx": "108", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\coupon\\coupon.tsx": "109", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\currencyList\\currencyList.tsx": "110", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\deliveryTimePopover\\deliveryTimePopover.tsx": "111", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\deliveryTimes\\deliveryTimes.tsx": "112", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\editPhone\\editPhone.tsx": "113", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\editPhone\\insertNewPhone.tsx": "114", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\editPhone\\newPhoneVerify.tsx": "115", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\empty\\empty.tsx": "116", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\emptyCart\\emptyCart.tsx": "117", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\extrasForm\\addonsForm.tsx": "118", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\extrasForm\\addonsItem.tsx": "119", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\extrasForm\\extrasForm.tsx": "120", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\fallbackImage\\fallbackImage.tsx": "121", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\favoriteBtn\\favoriteBtn.tsx": "122", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\favoriteBtn\\supportBtn.tsx": "123", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\fileUpload\\fileUpload.tsx": "124", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\filterPopover\\filterPopover.tsx": "125", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\groupOrderButton\\groupOrderButton.tsx": "126", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\groupOrderCard\\groupOrderCard.tsx": "127", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\groupOrderCard\\joinGroupCard.tsx": "128", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\icons.tsx": "129", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\imageUpload\\imageUpload.tsx": "130", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\checkboxInput.tsx": "131", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\customCheckbox.tsx": "132", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\datepicker.tsx": "133", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\multiSelect.tsx": "134", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\otpCodeInput.tsx": "135", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\outlinedInput.tsx": "136", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\passwordInput.tsx": "137", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\phoneInputWithVerification.tsx": "138", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\priceRangeSlider.tsx": "139", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\radioInput.tsx": "140", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\selectInput.tsx": "141", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\staticDatepicker.tsx": "142", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\switchInput.tsx": "143", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\textArea.tsx": "144", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\textInput.tsx": "145", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\languagePopover\\languagePopover.tsx": "146", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\loader\\loader.tsx": "147", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\loader\\loading.tsx": "148", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\loader\\pageLoading.tsx": "149", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\loginForm\\loginForm.tsx": "150", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\map\\map.tsx": "151", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\mobileSearch\\mobileSearch.tsx": "152", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\mobileShopCategories\\mobileProductCategories.tsx": "153", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\mobileShopCategories\\mobileShopCategories.tsx": "154", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\mobileShopCategories\\v2.tsx": "155", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\notificationCenter\\notificationCenter.tsx": "156", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\notificationCenterItem\\notificationCenterItem.tsx": "157", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\notificationStats\\notificationStats.tsx": "158", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\orderImage\\orderImage.tsx": "159", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\orderInfo\\orderInfo.tsx": "160", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\orderInfo\\parcelInfo.tsx": "161", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\orderListItem\\orderListItem.tsx": "162", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\orderProductItem\\orderProductItem.tsx": "163", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\orderProducts\\orderProducts.tsx": "164", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\orderProducts\\parcelDetails.tsx": "165", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\orderRefund\\orderRefund.tsx": "166", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\orderReview\\orderReview.tsx": "167", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\orderReview\\parcelReview.tsx": "168", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\orderReview\\styledRating.tsx": "169", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\ordersRefundButton\\ordersRefundButton.tsx": "170", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\otp-verify\\otpVerify.tsx": "171", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelCard\\parcelCard.tsx": "172", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelCard\\v2.tsx": "173", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelCard\\v3.tsx": "174", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelFeatureContainer\\featureButtons.tsx": "175", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelFeatureContainer\\parcelFeatureContainer.tsx": "176", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelFeatureItem\\featureLine.tsx": "177", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelFeatureItem\\parcelFeatureItem.tsx": "178", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelFeatureModal\\parcelFeatureModal.tsx": "179", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelFeaturesingle\\parcelFeatureSingle.tsx": "180", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelForm\\parcelForm.tsx": "181", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelForm\\parcelReceiver.tsx": "182", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelForm\\parcelSender.tsx": "183", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelOrderListItem\\parcleOrderListItem.tsx": "184", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelShow\\parcelShow.tsx": "185", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\paymentCategorySelector\\paymentCategorySelector.tsx": "186", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\paymentMethod\\paymentMethod.tsx": "187", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\payToUnPaidOrders\\payToUnpaidOrders.tsx": "188", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\pickers\\rcAddressPicker.tsx": "189", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\pickers\\rcDatePicker.tsx": "190", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\pickers\\rcDateTimePicker.tsx": "191", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\pickers\\rcParcelPicker.tsx": "192", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\pickers\\rcPersonPicker.tsx": "193", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\pickers\\rcSelect.tsx": "194", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\pickers\\rcShopSelect.tsx": "195", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\pickers\\rcZonePicker.tsx": "196", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\popularBadge\\popularBadge.tsx": "197", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\price\\price.tsx": "198", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\productCard\\productCard.tsx": "199", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\productGalleries\\productGalleries.tsx": "200", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\productShare\\productShare.tsx": "201", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\productSingle\\memberProductSingle.tsx": "202", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\productSingle\\productSingle.tsx": "203", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\productSingle\\productUI.tsx": "204", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\productSingle\\protectedProductSingle.tsx": "205", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\profileCard\\profileCard.tsx": "206", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\profileDropdown\\profileDropdown.tsx": "207", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\profilePassword\\profilePassword.tsx": "208", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\recipeCard\\recipeCard.tsx": "209", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\recipeContent\\recipeContent.tsx": "210", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\recipeHero\\recipeHero.tsx": "211", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\recipeIngredients\\recipeIngredients.tsx": "212", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\recipeIngredients\\recipeStockCard.tsx": "213", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\refundInfo\\refundInfo.tsx": "214", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\refundListItem\\refundListItem.tsx": "215", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\registerDetailsForm\\registerDetailsForm.tsx": "216", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\registerForm\\registerForm.tsx": "217", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\reservationFind\\reservationFind.tsx": "218", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\reservationHistoryItem\\reservationHistoryItem.tsx": "219", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\reservationTimes\\reservationTimes.tsx": "220", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\reservationTimes\\timeSlot.tsx": "221", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\resetPasswordForm\\resetPasswordForm.tsx": "222", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\restaurantListForm\\asyncRestaurantListForm.tsx": "223", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\savedLocationCard\\savedLocationCard.tsx": "224", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\searchResult\\searchResult.tsx": "225", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\searchResultItem\\productResultItem.tsx": "226", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\searchResultItem\\shopResultItem.tsx": "227", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\searchResultItem\\shopResultWithoutLink.tsx": "228", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\searchSuggestion\\searchSuggestion.tsx": "229", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\sendWalletMoney\\selectUsers.tsx": "230", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\sendWalletMoney\\sendWalletMoney.tsx": "231", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\seo.tsx": "232", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopBanner\\shopBanner.tsx": "233", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopCard\\shopCard.tsx": "234", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopCard\\v2.tsx": "235", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopCard\\v3.tsx": "236", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopCard\\v4.tsx": "237", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopCardDeliveryInfo\\shopCardDeliveryInfo.tsx": "238", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopCategoryHeader\\shopCategoryHeader.tsx": "239", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopFilter\\shopFilter.tsx": "240", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopForm\\parcelHeaderForm.tsx": "241", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopForm\\shopAddressForm.tsx": "242", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopForm\\shopDeliveryForm.tsx": "243", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopForm\\shopForm.tsx": "244", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopForm\\shopFormTypeTabs.tsx": "245", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopForm\\shopGeneralForm.tsx": "246", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopHeroCard\\shopHeroCard.tsx": "247", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopInfoDetails\\shopInfoDetails.tsx": "248", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopLogo\\shopLogo.tsx": "249", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopLogoBackground\\shopLogoBackground.tsx": "250", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopShare\\shopShare.tsx": "251", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopSorting\\shopSorting.tsx": "252", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\socialLogin\\socialLogin.tsx": "253", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\stepperComponent\\parcelStepper.tsx": "254", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\stepperComponent\\stepperComponent.tsx": "255", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storeCard\\storeCard.tsx": "256", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storeCard\\v2.tsx": "257", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storeCard\\v3.tsx": "258", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storyItem\\storyItem.tsx": "259", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storyItem\\storyLine.tsx": "260", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storyItem\\storyLinev4.tsx": "261", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storyItem\\v2.tsx": "262", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storyItem\\v4.tsx": "263", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storyMenu\\storyMenu.tsx": "264", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storyModal\\storyModal.tsx": "265", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storyModal\\v2.tsx": "266", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storyModal\\v4.tsx": "267", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storySingle\\storySingle.tsx": "268", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storySingle\\v2.tsx": "269", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storySingle\\v3.tsx": "270", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storySinglev4\\storySingle.tsx": "271", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\successModal\\successModal.tsx": "272", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\supportCard\\supportCard.tsx": "273", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\tip\\tip.tsx": "274", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\tip\\tipWithoutPayment.tsx": "275", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\unauthorized\\unauthorized.tsx": "276", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\updatePasswordForm\\updatePasswordForm.tsx": "277", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\verifiedComponent\\verifiedComponent.tsx": "278", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\verifyCodeForm\\verifyCodeForm.tsx": "279", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\verifyCodeForm\\verifyPhoneCode.tsx": "280", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\walletActionButtons\\walletActionButtons.tsx": "281", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\walletHistoryItem\\walletHistoryItem.tsx": "282", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\walletTopup\\walletTopup.tsx": "283", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\welcomeBlog\\welcomeBlog.tsx": "284", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\welcomeCard\\welcomeCard.tsx": "285", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\welcomeFeatures\\welcomeFeatures.tsx": "286", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\welcomeHeader\\welcomeHeader.tsx": "287", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\welcomeHero\\welcomeHero.tsx": "288", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\whyChooseUs\\whyChooseUs.tsx": "289", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\zoneNotFound\\zoneNotFound.tsx": "290", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\zoneShow\\zoneShow.tsx": "291", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\test-final-icons.tsx": "292", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\_error.tsx": "293"}, {"size": 174, "mtime": 1730813706000, "results": "294", "hashOfConfig": "295"}, {"size": 1542, "mtime": 1730813706000, "results": "296", "hashOfConfig": "295"}, {"size": 1741, "mtime": 1730813707000, "results": "297", "hashOfConfig": "295"}, {"size": 3671, "mtime": 1730813706000, "results": "298", "hashOfConfig": "295"}, {"size": 307, "mtime": 1730813707000, "results": "299", "hashOfConfig": "295"}, {"size": 2160, "mtime": 1730813706000, "results": "300", "hashOfConfig": "295"}, {"size": 2343, "mtime": 1730813707000, "results": "301", "hashOfConfig": "295"}, {"size": 1698, "mtime": 1730813707000, "results": "302", "hashOfConfig": "295"}, {"size": 2140, "mtime": 1730813706000, "results": "303", "hashOfConfig": "295"}, {"size": 1890, "mtime": 1730813707000, "results": "304", "hashOfConfig": "295"}, {"size": 1256, "mtime": 1730813707000, "results": "305", "hashOfConfig": "295"}, {"size": 1166, "mtime": 1730813706000, "results": "306", "hashOfConfig": "295"}, {"size": 712, "mtime": 1730813707000, "results": "307", "hashOfConfig": "295"}, {"size": 2282, "mtime": 1730813706000, "results": "308", "hashOfConfig": "295"}, {"size": 1101, "mtime": 1730813706000, "results": "309", "hashOfConfig": "295"}, {"size": 1720, "mtime": 1730813706000, "results": "310", "hashOfConfig": "295"}, {"size": 429, "mtime": 1730813706000, "results": "311", "hashOfConfig": "295"}, {"size": 2070, "mtime": 1730813706000, "results": "312", "hashOfConfig": "295"}, {"size": 3987, "mtime": 1730813707000, "results": "313", "hashOfConfig": "295"}, {"size": 2069, "mtime": 1730813707000, "results": "314", "hashOfConfig": "295"}, {"size": 2389, "mtime": 1730813706000, "results": "315", "hashOfConfig": "295"}, {"size": 3980, "mtime": 1730813707000, "results": "316", "hashOfConfig": "295"}, {"size": 2344, "mtime": 1730813707000, "results": "317", "hashOfConfig": "295"}, {"size": 1204, "mtime": 1730813706000, "results": "318", "hashOfConfig": "295"}, {"size": 355, "mtime": 1730813706000, "results": "319", "hashOfConfig": "295"}, {"size": 1747, "mtime": 1730813707000, "results": "320", "hashOfConfig": "295"}, {"size": 3181, "mtime": 1730813707000, "results": "321", "hashOfConfig": "295"}, {"size": 2895, "mtime": 1730813707000, "results": "322", "hashOfConfig": "295"}, {"size": 2394, "mtime": 1730813707000, "results": "323", "hashOfConfig": "295"}, {"size": 1396, "mtime": 1730813706000, "results": "324", "hashOfConfig": "295"}, {"size": 671, "mtime": 1730813706000, "results": "325", "hashOfConfig": "295"}, {"size": 2011, "mtime": 1730813706000, "results": "326", "hashOfConfig": "295"}, {"size": 2088, "mtime": 1730813707000, "results": "327", "hashOfConfig": "295"}, {"size": 1692, "mtime": 1730813707000, "results": "328", "hashOfConfig": "295"}, {"size": 1859, "mtime": 1730813706000, "results": "329", "hashOfConfig": "295"}, {"size": 3074, "mtime": 1730813707000, "results": "330", "hashOfConfig": "295"}, {"size": 9001, "mtime": 1730813707000, "results": "331", "hashOfConfig": "295"}, {"size": 1158, "mtime": 1730813706000, "results": "332", "hashOfConfig": "295"}, {"size": 779, "mtime": 1730813707000, "results": "333", "hashOfConfig": "295"}, {"size": 980, "mtime": 1730813707000, "results": "334", "hashOfConfig": "295"}, {"size": 10182, "mtime": 1730813707000, "results": "335", "hashOfConfig": "295"}, {"size": 1762, "mtime": 1730813707000, "results": "336", "hashOfConfig": "295"}, {"size": 1972, "mtime": 1730813707000, "results": "337", "hashOfConfig": "295"}, {"size": 1194, "mtime": 1730813706000, "results": "338", "hashOfConfig": "295"}, {"size": 391, "mtime": 1730813706000, "results": "339", "hashOfConfig": "295"}, {"size": 382, "mtime": 1730813706000, "results": "340", "hashOfConfig": "295"}, {"size": 364, "mtime": 1730813706000, "results": "341", "hashOfConfig": "295"}, {"size": 2280, "mtime": 1730813706000, "results": "342", "hashOfConfig": "295"}, {"size": 1881, "mtime": 1730813706000, "results": "343", "hashOfConfig": "295"}, {"size": 7111, "mtime": 1753409691355, "results": "344", "hashOfConfig": "295"}, {"size": 1753, "mtime": 1730813706000, "results": "345", "hashOfConfig": "295"}, {"size": 906, "mtime": 1730813692000, "results": "346", "hashOfConfig": "295"}, {"size": 11125, "mtime": 1753032911051, "results": "347", "hashOfConfig": "295"}, {"size": 6286, "mtime": 1730813692000, "results": "348", "hashOfConfig": "295"}, {"size": 1156, "mtime": 1730813692000, "results": "349", "hashOfConfig": "295"}, {"size": 2153, "mtime": 1753032814983, "results": "350", "hashOfConfig": "295"}, {"size": 653, "mtime": 1730813692000, "results": "351", "hashOfConfig": "295"}, {"size": 828, "mtime": 1730813692000, "results": "352", "hashOfConfig": "295"}, {"size": 674, "mtime": 1730813692000, "results": "353", "hashOfConfig": "295"}, {"size": 989, "mtime": 1730813692000, "results": "354", "hashOfConfig": "295"}, {"size": 3150, "mtime": 1730813692000, "results": "355", "hashOfConfig": "295"}, {"size": 6135, "mtime": 1753409609031, "results": "356", "hashOfConfig": "295"}, {"size": 4011, "mtime": 1730813692000, "results": "357", "hashOfConfig": "295"}, {"size": 576, "mtime": 1730813692000, "results": "358", "hashOfConfig": "295"}, {"size": 1385, "mtime": 1730813692000, "results": "359", "hashOfConfig": "295"}, {"size": 1559, "mtime": 1730813692000, "results": "360", "hashOfConfig": "295"}, {"size": 503, "mtime": 1730813692000, "results": "361", "hashOfConfig": "295"}, {"size": 673, "mtime": 1730813692000, "results": "362", "hashOfConfig": "295"}, {"size": 663, "mtime": 1730813692000, "results": "363", "hashOfConfig": "295"}, {"size": 1355, "mtime": 1730813693000, "results": "364", "hashOfConfig": "295"}, {"size": 489, "mtime": 1730813693000, "results": "365", "hashOfConfig": "295"}, {"size": 5395, "mtime": 1730813693000, "results": "366", "hashOfConfig": "295"}, {"size": 3089, "mtime": 1730813693000, "results": "367", "hashOfConfig": "295"}, {"size": 1153, "mtime": 1730813693000, "results": "368", "hashOfConfig": "295"}, {"size": 1192, "mtime": 1730813693000, "results": "369", "hashOfConfig": "295"}, {"size": 914, "mtime": 1730813693000, "results": "370", "hashOfConfig": "295"}, {"size": 982, "mtime": 1730813693000, "results": "371", "hashOfConfig": "295"}, {"size": 892, "mtime": 1730813693000, "results": "372", "hashOfConfig": "295"}, {"size": 732, "mtime": 1730813693000, "results": "373", "hashOfConfig": "295"}, {"size": 1045, "mtime": 1730813693000, "results": "374", "hashOfConfig": "295"}, {"size": 2296, "mtime": 1730813693000, "results": "375", "hashOfConfig": "295"}, {"size": 1158, "mtime": 1730813693000, "results": "376", "hashOfConfig": "295"}, {"size": 2012, "mtime": 1730813693000, "results": "377", "hashOfConfig": "295"}, {"size": 1569, "mtime": 1730813693000, "results": "378", "hashOfConfig": "295"}, {"size": 4207, "mtime": 1730813693000, "results": "379", "hashOfConfig": "295"}, {"size": 3536, "mtime": 1730813693000, "results": "380", "hashOfConfig": "295"}, {"size": 3341, "mtime": 1730813693000, "results": "381", "hashOfConfig": "295"}, {"size": 4317, "mtime": 1730813693000, "results": "382", "hashOfConfig": "295"}, {"size": 1805, "mtime": 1730813693000, "results": "383", "hashOfConfig": "295"}, {"size": 2463, "mtime": 1730813693000, "results": "384", "hashOfConfig": "295"}, {"size": 1896, "mtime": 1730813693000, "results": "385", "hashOfConfig": "295"}, {"size": 1016, "mtime": 1730813693000, "results": "386", "hashOfConfig": "295"}, {"size": 1018, "mtime": 1730813693000, "results": "387", "hashOfConfig": "295"}, {"size": 1168, "mtime": 1730813693000, "results": "388", "hashOfConfig": "295"}, {"size": 1218, "mtime": 1730813693000, "results": "389", "hashOfConfig": "295"}, {"size": 3759, "mtime": 1753371774964, "results": "390", "hashOfConfig": "295"}, {"size": 906, "mtime": 1730813693000, "results": "391", "hashOfConfig": "295"}, {"size": 1320, "mtime": 1730813693000, "results": "392", "hashOfConfig": "295"}, {"size": 6098, "mtime": 1752802906883, "results": "393", "hashOfConfig": "295"}, {"size": 506, "mtime": 1753411292768, "results": "394", "hashOfConfig": "295"}, {"size": 629, "mtime": 1730813693000, "results": "395", "hashOfConfig": "295"}, {"size": 2233, "mtime": 1730813693000, "results": "396", "hashOfConfig": "295"}, {"size": 1141, "mtime": 1730813693000, "results": "397", "hashOfConfig": "295"}, {"size": 6477, "mtime": 1730813694000, "results": "398", "hashOfConfig": "295"}, {"size": 1033, "mtime": 1730813694000, "results": "399", "hashOfConfig": "295"}, {"size": 1022, "mtime": 1730813694000, "results": "400", "hashOfConfig": "295"}, {"size": 1214, "mtime": 1730813694000, "results": "401", "hashOfConfig": "295"}, {"size": 1038, "mtime": 1730813694000, "results": "402", "hashOfConfig": "295"}, {"size": 2970, "mtime": 1730813694000, "results": "403", "hashOfConfig": "295"}, {"size": 1632, "mtime": 1730813694000, "results": "404", "hashOfConfig": "295"}, {"size": 2587, "mtime": 1730813694000, "results": "405", "hashOfConfig": "295"}, {"size": 5617, "mtime": 1730813694000, "results": "406", "hashOfConfig": "295"}, {"size": 1368, "mtime": 1730813694000, "results": "407", "hashOfConfig": "295"}, {"size": 2615, "mtime": 1730813694000, "results": "408", "hashOfConfig": "295"}, {"size": 4326, "mtime": 1730813694000, "results": "409", "hashOfConfig": "295"}, {"size": 899, "mtime": 1752802945699, "results": "410", "hashOfConfig": "295"}, {"size": 491, "mtime": 1730813694000, "results": "411", "hashOfConfig": "295"}, {"size": 2397, "mtime": 1730813694000, "results": "412", "hashOfConfig": "295"}, {"size": 2515, "mtime": 1730813694000, "results": "413", "hashOfConfig": "295"}, {"size": 1388, "mtime": 1730813694000, "results": "414", "hashOfConfig": "295"}, {"size": 1274, "mtime": 1752803041020, "results": "415", "hashOfConfig": "295"}, {"size": 483, "mtime": 1730813694000, "results": "416", "hashOfConfig": "295"}, {"size": 1106, "mtime": 1730813694000, "results": "417", "hashOfConfig": "295"}, {"size": 2207, "mtime": 1730813694000, "results": "418", "hashOfConfig": "295"}, {"size": 406, "mtime": 1730813694000, "results": "419", "hashOfConfig": "295"}, {"size": 3196, "mtime": 1730813694000, "results": "420", "hashOfConfig": "295"}, {"size": 6254, "mtime": 1730813694000, "results": "421", "hashOfConfig": "295"}, {"size": 2549, "mtime": 1730813694000, "results": "422", "hashOfConfig": "295"}, {"size": 21598, "mtime": 1753408654825, "results": "423", "hashOfConfig": "295"}, {"size": 2509, "mtime": 1752803219330, "results": "424", "hashOfConfig": "295"}, {"size": 401, "mtime": 1730813694000, "results": "425", "hashOfConfig": "295"}, {"size": 1403, "mtime": 1730813694000, "results": "426", "hashOfConfig": "295"}, {"size": 1228, "mtime": 1730813694000, "results": "427", "hashOfConfig": "295"}, {"size": 2659, "mtime": 1730813695000, "results": "428", "hashOfConfig": "295"}, {"size": 223, "mtime": 1730813695000, "results": "429", "hashOfConfig": "295"}, {"size": 1424, "mtime": 1730813695000, "results": "430", "hashOfConfig": "295"}, {"size": 1814, "mtime": 1730813695000, "results": "431", "hashOfConfig": "295"}, {"size": 2610, "mtime": 1730813695000, "results": "432", "hashOfConfig": "295"}, {"size": 1146, "mtime": 1730813695000, "results": "433", "hashOfConfig": "295"}, {"size": 1179, "mtime": 1730813695000, "results": "434", "hashOfConfig": "295"}, {"size": 2214, "mtime": 1730813695000, "results": "435", "hashOfConfig": "295"}, {"size": 1159, "mtime": 1730813695000, "results": "436", "hashOfConfig": "295"}, {"size": 1625, "mtime": 1730813695000, "results": "437", "hashOfConfig": "295"}, {"size": 1242, "mtime": 1730813695000, "results": "438", "hashOfConfig": "295"}, {"size": 1226, "mtime": 1730813695000, "results": "439", "hashOfConfig": "295"}, {"size": 1871, "mtime": 1752336168000, "results": "440", "hashOfConfig": "295"}, {"size": 293, "mtime": 1730813695000, "results": "441", "hashOfConfig": "295"}, {"size": 272, "mtime": 1730813695000, "results": "442", "hashOfConfig": "295"}, {"size": 280, "mtime": 1730813695000, "results": "443", "hashOfConfig": "295"}, {"size": 4820, "mtime": 1752802890133, "results": "444", "hashOfConfig": "295"}, {"size": 4563, "mtime": 1730813695000, "results": "445", "hashOfConfig": "295"}, {"size": 886, "mtime": 1730813695000, "results": "446", "hashOfConfig": "295"}, {"size": 966, "mtime": 1730813695000, "results": "447", "hashOfConfig": "295"}, {"size": 1412, "mtime": 1730813695000, "results": "448", "hashOfConfig": "295"}, {"size": 1140, "mtime": 1730813695000, "results": "449", "hashOfConfig": "295"}, {"size": 6253, "mtime": 1730813695000, "results": "450", "hashOfConfig": "295"}, {"size": 1534, "mtime": 1730813695000, "results": "451", "hashOfConfig": "295"}, {"size": 1748, "mtime": 1730813695000, "results": "452", "hashOfConfig": "295"}, {"size": 1209, "mtime": 1730813695000, "results": "453", "hashOfConfig": "295"}, {"size": 15353, "mtime": 1753411829917, "results": "454", "hashOfConfig": "295"}, {"size": 4583, "mtime": 1730813695000, "results": "455", "hashOfConfig": "295"}, {"size": 1840, "mtime": 1730813695000, "results": "456", "hashOfConfig": "295"}, {"size": 2692, "mtime": 1730813695000, "results": "457", "hashOfConfig": "295"}, {"size": 701, "mtime": 1730813695000, "results": "458", "hashOfConfig": "295"}, {"size": 1474, "mtime": 1730813695000, "results": "459", "hashOfConfig": "295"}, {"size": 2216, "mtime": 1730813695000, "results": "460", "hashOfConfig": "295"}, {"size": 2637, "mtime": 1730813696000, "results": "461", "hashOfConfig": "295"}, {"size": 2682, "mtime": 1730813696000, "results": "462", "hashOfConfig": "295"}, {"size": 574, "mtime": 1730813696000, "results": "463", "hashOfConfig": "295"}, {"size": 850, "mtime": 1730813696000, "results": "464", "hashOfConfig": "295"}, {"size": 5682, "mtime": 1730813696000, "results": "465", "hashOfConfig": "295"}, {"size": 590, "mtime": 1752803144764, "results": "466", "hashOfConfig": "295"}, {"size": 1104, "mtime": 1730813696000, "results": "467", "hashOfConfig": "295"}, {"size": 1272, "mtime": 1730813696000, "results": "468", "hashOfConfig": "295"}, {"size": 907, "mtime": 1730813696000, "results": "469", "hashOfConfig": "295"}, {"size": 2425, "mtime": 1730813696000, "results": "470", "hashOfConfig": "295"}, {"size": 717, "mtime": 1730813696000, "results": "471", "hashOfConfig": "295"}, {"size": 1846, "mtime": 1730813696000, "results": "472", "hashOfConfig": "295"}, {"size": 893, "mtime": 1730813696000, "results": "473", "hashOfConfig": "295"}, {"size": 1568, "mtime": 1730813696000, "results": "474", "hashOfConfig": "295"}, {"size": 3915, "mtime": 1730813696000, "results": "475", "hashOfConfig": "295"}, {"size": 8689, "mtime": 1730813696000, "results": "476", "hashOfConfig": "295"}, {"size": 4046, "mtime": 1730813696000, "results": "477", "hashOfConfig": "295"}, {"size": 2199, "mtime": 1730813696000, "results": "478", "hashOfConfig": "295"}, {"size": 1709, "mtime": 1730813696000, "results": "479", "hashOfConfig": "295"}, {"size": 1731, "mtime": 1753394524934, "results": "480", "hashOfConfig": "295"}, {"size": 3367, "mtime": 1753394459669, "results": "481", "hashOfConfig": "295"}, {"size": 4896, "mtime": 1730813696000, "results": "482", "hashOfConfig": "295"}, {"size": 1763, "mtime": 1730813696000, "results": "483", "hashOfConfig": "295"}, {"size": 1935, "mtime": 1730813696000, "results": "484", "hashOfConfig": "295"}, {"size": 3147, "mtime": 1753411272478, "results": "485", "hashOfConfig": "295"}, {"size": 3499, "mtime": 1730813696000, "results": "486", "hashOfConfig": "295"}, {"size": 1950, "mtime": 1730813696000, "results": "487", "hashOfConfig": "295"}, {"size": 2231, "mtime": 1730813696000, "results": "488", "hashOfConfig": "295"}, {"size": 5000, "mtime": 1730813696000, "results": "489", "hashOfConfig": "295"}, {"size": 2686, "mtime": 1730813696000, "results": "490", "hashOfConfig": "295"}, {"size": 439, "mtime": 1730813696000, "results": "491", "hashOfConfig": "295"}, {"size": 871, "mtime": 1730813696000, "results": "492", "hashOfConfig": "295"}, {"size": 2137, "mtime": 1730813696000, "results": "493", "hashOfConfig": "295"}, {"size": 1344, "mtime": 1730813697000, "results": "494", "hashOfConfig": "295"}, {"size": 3270, "mtime": 1730813697000, "results": "495", "hashOfConfig": "295"}, {"size": 6919, "mtime": 1730813697000, "results": "496", "hashOfConfig": "295"}, {"size": 6089, "mtime": 1730813697000, "results": "497", "hashOfConfig": "295"}, {"size": 5170, "mtime": 1730813697000, "results": "498", "hashOfConfig": "295"}, {"size": 7191, "mtime": 1730813697000, "results": "499", "hashOfConfig": "295"}, {"size": 1277, "mtime": 1730813697000, "results": "500", "hashOfConfig": "295"}, {"size": 6991, "mtime": 1730813697000, "results": "501", "hashOfConfig": "295"}, {"size": 4844, "mtime": 1730813697000, "results": "502", "hashOfConfig": "295"}, {"size": 1986, "mtime": 1730813697000, "results": "503", "hashOfConfig": "295"}, {"size": 1353, "mtime": 1730813697000, "results": "504", "hashOfConfig": "295"}, {"size": 1540, "mtime": 1730813697000, "results": "505", "hashOfConfig": "295"}, {"size": 4889, "mtime": 1730813697000, "results": "506", "hashOfConfig": "295"}, {"size": 2355, "mtime": 1730813697000, "results": "507", "hashOfConfig": "295"}, {"size": 1318, "mtime": 1730813697000, "results": "508", "hashOfConfig": "295"}, {"size": 1692, "mtime": 1730813697000, "results": "509", "hashOfConfig": "295"}, {"size": 6715, "mtime": 1730813697000, "results": "510", "hashOfConfig": "295"}, {"size": 3840, "mtime": 1730813697000, "results": "511", "hashOfConfig": "295"}, {"size": 3868, "mtime": 1730813697000, "results": "512", "hashOfConfig": "295"}, {"size": 1637, "mtime": 1730813697000, "results": "513", "hashOfConfig": "295"}, {"size": 6740, "mtime": 1730813697000, "results": "514", "hashOfConfig": "295"}, {"size": 770, "mtime": 1730813697000, "results": "515", "hashOfConfig": "295"}, {"size": 3876, "mtime": 1730813697000, "results": "516", "hashOfConfig": "295"}, {"size": 2686, "mtime": 1730813697000, "results": "517", "hashOfConfig": "295"}, {"size": 1080, "mtime": 1730813697000, "results": "518", "hashOfConfig": "295"}, {"size": 4330, "mtime": 1730813697000, "results": "519", "hashOfConfig": "295"}, {"size": 1239, "mtime": 1730813697000, "results": "520", "hashOfConfig": "295"}, {"size": 782, "mtime": 1730813697000, "results": "521", "hashOfConfig": "295"}, {"size": 1770, "mtime": 1730813698000, "results": "522", "hashOfConfig": "295"}, {"size": 1831, "mtime": 1730813698000, "results": "523", "hashOfConfig": "295"}, {"size": 2095, "mtime": 1730813698000, "results": "524", "hashOfConfig": "295"}, {"size": 3791, "mtime": 1730813698000, "results": "525", "hashOfConfig": "295"}, {"size": 1650, "mtime": 1730813692000, "results": "526", "hashOfConfig": "295"}, {"size": 1445, "mtime": 1730813698000, "results": "527", "hashOfConfig": "295"}, {"size": 2537, "mtime": 1730813698000, "results": "528", "hashOfConfig": "295"}, {"size": 2144, "mtime": 1730813698000, "results": "529", "hashOfConfig": "295"}, {"size": 1698, "mtime": 1730813698000, "results": "530", "hashOfConfig": "295"}, {"size": 2069, "mtime": 1730813698000, "results": "531", "hashOfConfig": "295"}, {"size": 668, "mtime": 1730813698000, "results": "532", "hashOfConfig": "295"}, {"size": 1046, "mtime": 1730813698000, "results": "533", "hashOfConfig": "295"}, {"size": 5431, "mtime": 1730813698000, "results": "534", "hashOfConfig": "295"}, {"size": 3032, "mtime": 1730813698000, "results": "535", "hashOfConfig": "295"}, {"size": 1846, "mtime": 1730813698000, "results": "536", "hashOfConfig": "295"}, {"size": 2789, "mtime": 1730813698000, "results": "537", "hashOfConfig": "295"}, {"size": 1252, "mtime": 1730813698000, "results": "538", "hashOfConfig": "295"}, {"size": 954, "mtime": 1730813698000, "results": "539", "hashOfConfig": "295"}, {"size": 4391, "mtime": 1730813698000, "results": "540", "hashOfConfig": "295"}, {"size": 1183, "mtime": 1730813698000, "results": "541", "hashOfConfig": "295"}, {"size": 5741, "mtime": 1730813698000, "results": "542", "hashOfConfig": "295"}, {"size": 684, "mtime": 1730813698000, "results": "543", "hashOfConfig": "295"}, {"size": 926, "mtime": 1730813698000, "results": "544", "hashOfConfig": "295"}, {"size": 3182, "mtime": 1730813698000, "results": "545", "hashOfConfig": "295"}, {"size": 1355, "mtime": 1730813698000, "results": "546", "hashOfConfig": "295"}, {"size": 4162, "mtime": 1730813698000, "results": "547", "hashOfConfig": "295"}, {"size": 3078, "mtime": 1730813698000, "results": "548", "hashOfConfig": "295"}, {"size": 3114, "mtime": 1730813698000, "results": "549", "hashOfConfig": "295"}, {"size": 1597, "mtime": 1730813698000, "results": "550", "hashOfConfig": "295"}, {"size": 1222, "mtime": 1730813699000, "results": "551", "hashOfConfig": "295"}, {"size": 1290, "mtime": 1730813699000, "results": "552", "hashOfConfig": "295"}, {"size": 3155, "mtime": 1730813699000, "results": "553", "hashOfConfig": "295"}, {"size": 708, "mtime": 1730813699000, "results": "554", "hashOfConfig": "295"}, {"size": 701, "mtime": 1730813699000, "results": "555", "hashOfConfig": "295"}, {"size": 2941, "mtime": 1730813699000, "results": "556", "hashOfConfig": "295"}, {"size": 2102, "mtime": 1753411761158, "results": "557", "hashOfConfig": "295"}, {"size": 1651, "mtime": 1730813699000, "results": "558", "hashOfConfig": "295"}, {"size": 861, "mtime": 1730813699000, "results": "559", "hashOfConfig": "295"}, {"size": 853, "mtime": 1730813699000, "results": "560", "hashOfConfig": "295"}, {"size": 892, "mtime": 1730813699000, "results": "561", "hashOfConfig": "295"}, {"size": 1974, "mtime": 1730813699000, "results": "562", "hashOfConfig": "295"}, {"size": 1983, "mtime": 1730813699000, "results": "563", "hashOfConfig": "295"}, {"size": 1667, "mtime": 1730813699000, "results": "564", "hashOfConfig": "295"}, {"size": 1534, "mtime": 1730813699000, "results": "565", "hashOfConfig": "295"}, {"size": 1263, "mtime": 1730813699000, "results": "566", "hashOfConfig": "295"}, {"size": 1619, "mtime": 1730813699000, "results": "567", "hashOfConfig": "295"}, {"size": 9834, "mtime": 1730813699000, "results": "568", "hashOfConfig": "295"}, {"size": 3235, "mtime": 1730813699000, "results": "569", "hashOfConfig": "295"}, {"size": 769, "mtime": 1752802961110, "results": "570", "hashOfConfig": "295"}, {"size": 2231, "mtime": 1730813699000, "results": "571", "hashOfConfig": "295"}, {"size": 265, "mtime": 1730813699000, "results": "572", "hashOfConfig": "295"}, {"size": 3833, "mtime": 1753411789002, "results": "573", "hashOfConfig": "295"}, {"size": 4961, "mtime": 1753411803211, "results": "574", "hashOfConfig": "295"}, {"size": 2703, "mtime": 1730813699000, "results": "575", "hashOfConfig": "295"}, {"size": 2054, "mtime": 1730813699000, "results": "576", "hashOfConfig": "295"}, {"size": 4377, "mtime": 1730813699000, "results": "577", "hashOfConfig": "295"}, {"size": 3695, "mtime": 1730813699000, "results": "578", "hashOfConfig": "295"}, {"size": 1360, "mtime": 1730813699000, "results": "579", "hashOfConfig": "295"}, {"size": 2200, "mtime": 1752802988357, "results": "580", "hashOfConfig": "295"}, {"size": 2080, "mtime": 1730813700000, "results": "581", "hashOfConfig": "295"}, {"size": 4077, "mtime": 1730813700000, "results": "582", "hashOfConfig": "295"}, {"size": 1164, "mtime": 1752796500071, "results": "583", "hashOfConfig": "295"}, {"size": 451, "mtime": 1730813700000, "results": "584", "hashOfConfig": "295"}, {"size": 1889, "mtime": 1730813700000, "results": "585", "hashOfConfig": "295"}, {"size": 12472, "mtime": 1753408751910, "results": "586", "hashOfConfig": "295"}, {"size": 2184, "mtime": 1753409674777, "results": "587", "hashOfConfig": "295"}, {"filePath": "588", "messages": "589", "suppressedMessages": "590", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "dq7tu7", {"filePath": "591", "messages": "592", "suppressedMessages": "593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "594", "messages": "595", "suppressedMessages": "596", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "597"}, {"filePath": "598", "messages": "599", "suppressedMessages": "600", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "601", "messages": "602", "suppressedMessages": "603", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "604", "messages": "605", "suppressedMessages": "606", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "607", "messages": "608", "suppressedMessages": "609", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "610"}, {"filePath": "611", "messages": "612", "suppressedMessages": "613", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "614", "messages": "615", "suppressedMessages": "616", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "617"}, {"filePath": "618", "messages": "619", "suppressedMessages": "620", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "621", "messages": "622", "suppressedMessages": "623", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "624", "messages": "625", "suppressedMessages": "626", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "627", "messages": "628", "suppressedMessages": "629", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "630", "messages": "631", "suppressedMessages": "632", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "633"}, {"filePath": "634", "messages": "635", "suppressedMessages": "636", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "637", "messages": "638", "suppressedMessages": "639", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "640", "messages": "641", "suppressedMessages": "642", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "643", "messages": "644", "suppressedMessages": "645", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "646", "messages": "647", "suppressedMessages": "648", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "649", "messages": "650", "suppressedMessages": "651", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "652", "messages": "653", "suppressedMessages": "654", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "655", "messages": "656", "suppressedMessages": "657", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "658", "messages": "659", "suppressedMessages": "660", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "661", "messages": "662", "suppressedMessages": "663", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "664", "messages": "665", "suppressedMessages": "666", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "667", "messages": "668", "suppressedMessages": "669", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "670"}, {"filePath": "671", "messages": "672", "suppressedMessages": "673", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "674", "messages": "675", "suppressedMessages": "676", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "677", "messages": "678", "suppressedMessages": "679", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "680", "messages": "681", "suppressedMessages": "682", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "683", "messages": "684", "suppressedMessages": "685", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "686", "messages": "687", "suppressedMessages": "688", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "689", "messages": "690", "suppressedMessages": "691", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "692", "messages": "693", "suppressedMessages": "694", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "695", "messages": "696", "suppressedMessages": "697", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "698", "messages": "699", "suppressedMessages": "700", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "701", "messages": "702", "suppressedMessages": "703", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "704", "messages": "705", "suppressedMessages": "706", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "707", "messages": "708", "suppressedMessages": "709", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "710", "messages": "711", "suppressedMessages": "712", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "713", "messages": "714", "suppressedMessages": "715", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "716", "messages": "717", "suppressedMessages": "718", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "719"}, {"filePath": "720", "messages": "721", "suppressedMessages": "722", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "723", "messages": "724", "suppressedMessages": "725", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "726", "messages": "727", "suppressedMessages": "728", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "729", "messages": "730", "suppressedMessages": "731", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "732", "messages": "733", "suppressedMessages": "734", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "735", "messages": "736", "suppressedMessages": "737", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "738", "messages": "739", "suppressedMessages": "740", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "741", "messages": "742", "suppressedMessages": "743", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "744", "messages": "745", "suppressedMessages": "746", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "747", "messages": "748", "suppressedMessages": "749", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "750", "messages": "751", "suppressedMessages": "752", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "753", "messages": "754", "suppressedMessages": "755", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "756", "messages": "757", "suppressedMessages": "758", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "759", "messages": "760", "suppressedMessages": "761", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "762", "messages": "763", "suppressedMessages": "764", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "765", "messages": "766", "suppressedMessages": "767", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "768", "messages": "769", "suppressedMessages": "770", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "771", "messages": "772", "suppressedMessages": "773", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "774", "messages": "775", "suppressedMessages": "776", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "777", "messages": "778", "suppressedMessages": "779", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "780", "messages": "781", "suppressedMessages": "782", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "783", "messages": "784", "suppressedMessages": "785", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "786", "messages": "787", "suppressedMessages": "788", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "789", "messages": "790", "suppressedMessages": "791", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "792", "messages": "793", "suppressedMessages": "794", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "795", "messages": "796", "suppressedMessages": "797", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "798", "messages": "799", "suppressedMessages": "800", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "801", "messages": "802", "suppressedMessages": "803", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "804", "messages": "805", "suppressedMessages": "806", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "807", "messages": "808", "suppressedMessages": "809", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "810", "messages": "811", "suppressedMessages": "812", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "813", "messages": "814", "suppressedMessages": "815", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "816", "messages": "817", "suppressedMessages": "818", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "819", "messages": "820", "suppressedMessages": "821", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "822", "messages": "823", "suppressedMessages": "824", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "825", "messages": "826", "suppressedMessages": "827", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "828", "messages": "829", "suppressedMessages": "830", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "831", "messages": "832", "suppressedMessages": "833", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "834", "messages": "835", "suppressedMessages": "836", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "837", "messages": "838", "suppressedMessages": "839", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "840", "messages": "841", "suppressedMessages": "842", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "843", "messages": "844", "suppressedMessages": "845", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "846", "messages": "847", "suppressedMessages": "848", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "849", "messages": "850", "suppressedMessages": "851", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "852", "messages": "853", "suppressedMessages": "854", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "855", "messages": "856", "suppressedMessages": "857", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "858", "messages": "859", "suppressedMessages": "860", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "861", "messages": "862", "suppressedMessages": "863", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "864", "messages": "865", "suppressedMessages": "866", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "867", "messages": "868", "suppressedMessages": "869", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "870", "messages": "871", "suppressedMessages": "872", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "873", "messages": "874", "suppressedMessages": "875", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "876", "messages": "877", "suppressedMessages": "878", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "879", "messages": "880", "suppressedMessages": "881", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "882", "messages": "883", "suppressedMessages": "884", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "885", "messages": "886", "suppressedMessages": "887", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "888", "messages": "889", "suppressedMessages": "890", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "891"}, {"filePath": "892", "messages": "893", "suppressedMessages": "894", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "895", "messages": "896", "suppressedMessages": "897", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "898", "messages": "899", "suppressedMessages": "900", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "901"}, {"filePath": "902", "messages": "903", "suppressedMessages": "904", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "905", "messages": "906", "suppressedMessages": "907", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "908", "messages": "909", "suppressedMessages": "910", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "911", "messages": "912", "suppressedMessages": "913", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "914", "messages": "915", "suppressedMessages": "916", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "917", "messages": "918", "suppressedMessages": "919", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "920", "messages": "921", "suppressedMessages": "922", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "923", "messages": "924", "suppressedMessages": "925", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "926", "messages": "927", "suppressedMessages": "928", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "929", "messages": "930", "suppressedMessages": "931", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "932", "messages": "933", "suppressedMessages": "934", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "935", "messages": "936", "suppressedMessages": "937", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "938", "messages": "939", "suppressedMessages": "940", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "941"}, {"filePath": "942", "messages": "943", "suppressedMessages": "944", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "945", "messages": "946", "suppressedMessages": "947", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "948", "messages": "949", "suppressedMessages": "950", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "951", "messages": "952", "suppressedMessages": "953", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "954", "messages": "955", "suppressedMessages": "956", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "957", "messages": "958", "suppressedMessages": "959", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "960", "messages": "961", "suppressedMessages": "962", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "963", "messages": "964", "suppressedMessages": "965", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "966", "messages": "967", "suppressedMessages": "968", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "969", "messages": "970", "suppressedMessages": "971", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "972", "messages": "973", "suppressedMessages": "974", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "975", "messages": "976", "suppressedMessages": "977", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "978", "messages": "979", "suppressedMessages": "980", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "981", "messages": "982", "suppressedMessages": "983", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "984", "messages": "985", "suppressedMessages": "986", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "987", "messages": "988", "suppressedMessages": "989", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "990", "messages": "991", "suppressedMessages": "992", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "993", "messages": "994", "suppressedMessages": "995", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "996", "messages": "997", "suppressedMessages": "998", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "999", "messages": "1000", "suppressedMessages": "1001", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1002", "messages": "1003", "suppressedMessages": "1004", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1005", "messages": "1006", "suppressedMessages": "1007", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1008", "messages": "1009", "suppressedMessages": "1010", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1011", "messages": "1012", "suppressedMessages": "1013", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1014", "messages": "1015", "suppressedMessages": "1016", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1017", "messages": "1018", "suppressedMessages": "1019", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1020", "messages": "1021", "suppressedMessages": "1022", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1023", "messages": "1024", "suppressedMessages": "1025", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1026", "messages": "1027", "suppressedMessages": "1028", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1029", "messages": "1030", "suppressedMessages": "1031", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1032", "messages": "1033", "suppressedMessages": "1034", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1035", "messages": "1036", "suppressedMessages": "1037", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1038", "messages": "1039", "suppressedMessages": "1040", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1041", "messages": "1042", "suppressedMessages": "1043", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1044", "messages": "1045", "suppressedMessages": "1046", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1047", "messages": "1048", "suppressedMessages": "1049", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "1050"}, {"filePath": "1051", "messages": "1052", "suppressedMessages": "1053", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1054", "messages": "1055", "suppressedMessages": "1056", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1057", "messages": "1058", "suppressedMessages": "1059", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1060", "messages": "1061", "suppressedMessages": "1062", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1063", "messages": "1064", "suppressedMessages": "1065", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1066", "messages": "1067", "suppressedMessages": "1068", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1069", "messages": "1070", "suppressedMessages": "1071", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1072", "messages": "1073", "suppressedMessages": "1074", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "1075"}, {"filePath": "1076", "messages": "1077", "suppressedMessages": "1078", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1079", "messages": "1080", "suppressedMessages": "1081", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1082", "messages": "1083", "suppressedMessages": "1084", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1085", "messages": "1086", "suppressedMessages": "1087", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1088", "messages": "1089", "suppressedMessages": "1090", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1091", "messages": "1092", "suppressedMessages": "1093", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1094", "messages": "1095", "suppressedMessages": "1096", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1097", "messages": "1098", "suppressedMessages": "1099", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1100", "messages": "1101", "suppressedMessages": "1102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1103", "messages": "1104", "suppressedMessages": "1105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1106", "messages": "1107", "suppressedMessages": "1108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1109", "messages": "1110", "suppressedMessages": "1111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "1112"}, {"filePath": "1113", "messages": "1114", "suppressedMessages": "1115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1116", "messages": "1117", "suppressedMessages": "1118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1119", "messages": "1120", "suppressedMessages": "1121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1122", "messages": "1123", "suppressedMessages": "1124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1125", "messages": "1126", "suppressedMessages": "1127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1128", "messages": "1129", "suppressedMessages": "1130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1131", "messages": "1132", "suppressedMessages": "1133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "1134"}, {"filePath": "1135", "messages": "1136", "suppressedMessages": "1137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1138", "messages": "1139", "suppressedMessages": "1140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1141", "messages": "1142", "suppressedMessages": "1143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1144", "messages": "1145", "suppressedMessages": "1146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1147", "messages": "1148", "suppressedMessages": "1149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1150", "messages": "1151", "suppressedMessages": "1152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1153", "messages": "1154", "suppressedMessages": "1155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1156", "messages": "1157", "suppressedMessages": "1158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1159", "messages": "1160", "suppressedMessages": "1161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "1162"}, {"filePath": "1163", "messages": "1164", "suppressedMessages": "1165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1166", "messages": "1167", "suppressedMessages": "1168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1169", "messages": "1170", "suppressedMessages": "1171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1172", "messages": "1173", "suppressedMessages": "1174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1175", "messages": "1176", "suppressedMessages": "1177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1178", "messages": "1179", "suppressedMessages": "1180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1181", "messages": "1182", "suppressedMessages": "1183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1184", "messages": "1185", "suppressedMessages": "1186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1187", "messages": "1188", "suppressedMessages": "1189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1190", "messages": "1191", "suppressedMessages": "1192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1193", "messages": "1194", "suppressedMessages": "1195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1196", "messages": "1197", "suppressedMessages": "1198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1199", "messages": "1200", "suppressedMessages": "1201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1202", "messages": "1203", "suppressedMessages": "1204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1205", "messages": "1206", "suppressedMessages": "1207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1208", "messages": "1209", "suppressedMessages": "1210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1211", "messages": "1212", "suppressedMessages": "1213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1214", "messages": "1215", "suppressedMessages": "1216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1217", "messages": "1218", "suppressedMessages": "1219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1220", "messages": "1221", "suppressedMessages": "1222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1223", "messages": "1224", "suppressedMessages": "1225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1226", "messages": "1227", "suppressedMessages": "1228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1229", "messages": "1230", "suppressedMessages": "1231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1232", "messages": "1233", "suppressedMessages": "1234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1235", "messages": "1236", "suppressedMessages": "1237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1238", "messages": "1239", "suppressedMessages": "1240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1241", "messages": "1242", "suppressedMessages": "1243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1244", "messages": "1245", "suppressedMessages": "1246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1247", "messages": "1248", "suppressedMessages": "1249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1250", "messages": "1251", "suppressedMessages": "1252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1253", "messages": "1254", "suppressedMessages": "1255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1256", "messages": "1257", "suppressedMessages": "1258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1259", "messages": "1260", "suppressedMessages": "1261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1262", "messages": "1263", "suppressedMessages": "1264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1265", "messages": "1266", "suppressedMessages": "1267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1268", "messages": "1269", "suppressedMessages": "1270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1271", "messages": "1272", "suppressedMessages": "1273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1274", "messages": "1275", "suppressedMessages": "1276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1277", "messages": "1278", "suppressedMessages": "1279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1280", "messages": "1281", "suppressedMessages": "1282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1283", "messages": "1284", "suppressedMessages": "1285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1286", "messages": "1287", "suppressedMessages": "1288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1289", "messages": "1290", "suppressedMessages": "1291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1292", "messages": "1293", "suppressedMessages": "1294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1295", "messages": "1296", "suppressedMessages": "1297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1298", "messages": "1299", "suppressedMessages": "1300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1301", "messages": "1302", "suppressedMessages": "1303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1304", "messages": "1305", "suppressedMessages": "1306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1307", "messages": "1308", "suppressedMessages": "1309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1310", "messages": "1311", "suppressedMessages": "1312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1313", "messages": "1314", "suppressedMessages": "1315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1316", "messages": "1317", "suppressedMessages": "1318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1319", "messages": "1320", "suppressedMessages": "1321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1322", "messages": "1323", "suppressedMessages": "1324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1325", "messages": "1326", "suppressedMessages": "1327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "1328"}, {"filePath": "1329", "messages": "1330", "suppressedMessages": "1331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1332", "messages": "1333", "suppressedMessages": "1334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1335", "messages": "1336", "suppressedMessages": "1337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1338", "messages": "1339", "suppressedMessages": "1340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1341", "messages": "1342", "suppressedMessages": "1343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1344", "messages": "1345", "suppressedMessages": "1346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1347", "messages": "1348", "suppressedMessages": "1349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1350", "messages": "1351", "suppressedMessages": "1352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1353", "messages": "1354", "suppressedMessages": "1355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1356", "messages": "1357", "suppressedMessages": "1358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1359", "messages": "1360", "suppressedMessages": "1361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1362", "messages": "1363", "suppressedMessages": "1364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1365", "messages": "1366", "suppressedMessages": "1367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1368", "messages": "1369", "suppressedMessages": "1370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1371", "messages": "1372", "suppressedMessages": "1373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1374", "messages": "1375", "suppressedMessages": "1376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1377", "messages": "1378", "suppressedMessages": "1379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "1380"}, {"filePath": "1381", "messages": "1382", "suppressedMessages": "1383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1384", "messages": "1385", "suppressedMessages": "1386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1387", "messages": "1388", "suppressedMessages": "1389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "1390"}, {"filePath": "1391", "messages": "1392", "suppressedMessages": "1393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1394", "messages": "1395", "suppressedMessages": "1396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1397", "messages": "1398", "suppressedMessages": "1399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1400", "messages": "1401", "suppressedMessages": "1402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1403", "messages": "1404", "suppressedMessages": "1405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1406", "messages": "1407", "suppressedMessages": "1408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1409", "messages": "1410", "suppressedMessages": "1411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1412", "messages": "1413", "suppressedMessages": "1414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1415", "messages": "1416", "suppressedMessages": "1417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1418", "messages": "1419", "suppressedMessages": "1420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1421", "messages": "1422", "suppressedMessages": "1423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1424", "messages": "1425", "suppressedMessages": "1426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1427", "messages": "1428", "suppressedMessages": "1429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1430", "messages": "1431", "suppressedMessages": "1432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1433", "messages": "1434", "suppressedMessages": "1435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1436", "messages": "1437", "suppressedMessages": "1438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1439", "messages": "1440", "suppressedMessages": "1441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1442", "messages": "1443", "suppressedMessages": "1444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1445", "messages": "1446", "suppressedMessages": "1447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1448", "messages": "1449", "suppressedMessages": "1450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1451", "messages": "1452", "suppressedMessages": "1453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1454", "messages": "1455", "suppressedMessages": "1456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1457", "messages": "1458", "suppressedMessages": "1459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1460", "messages": "1461", "suppressedMessages": "1462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1463", "messages": "1464", "suppressedMessages": "1465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1466", "messages": "1467", "suppressedMessages": "1468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1469", "messages": "1470", "suppressedMessages": "1471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1472", "messages": "1473", "suppressedMessages": "1474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1475", "messages": "1476", "suppressedMessages": "1477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1478", "messages": "1479", "suppressedMessages": "1480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1481", "messages": "1482", "suppressedMessages": "1483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\404.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\about.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\ads\\index.tsx", ["1484"], [], "import React, { useCallback, useEffect, useRef } from \"react\";\nimport SEO from \"components/seo\";\nimport useLocale from \"hooks/useLocale\";\nimport { useInfiniteQuery } from \"react-query\";\nimport bannerService from \"services/banner\";\nimport BannerList from \"containers/bannerList/v4\";\nimport Loader from \"components/loader/loader\";\n\ntype Props = {};\n\nexport default function Ads({}: Props) {\n  const { t, locale } = useLocale();\n  const loader = useRef(null);\n\n  const {\n    data,\n    error,\n    fetchNextPage,\n    hasNextPage,\n    isFetchingNextPage,\n    isLoading,\n  } = useInfiniteQuery(\n    [\"adsPaginate\", locale],\n    ({ pageParam = 1 }) =>\n      bannerService.getAllAds({\n        page: pageParam,\n        perPage: 10,\n      }),\n    {\n      getNextPageParam: (lastPage: any) => {\n        if (lastPage.meta.current_page < lastPage.meta.last_page) {\n          return lastPage.meta.current_page + 1;\n        }\n        return undefined;\n      },\n    }\n  );\n  const banners = data?.pages?.flatMap((item) => item.data) || [];\n\n  const handleObserver = useCallback((entries: any) => {\n    const target = entries[0];\n    if (target.isIntersecting && hasNextPage) {\n      fetchNextPage();\n    }\n  }, []);\n\n  useEffect(() => {\n    const option = {\n      root: null,\n      rootMargin: \"20px\",\n      threshold: 0,\n    };\n    const observer = new IntersectionObserver(handleObserver, option);\n    if (loader.current) observer.observe(loader.current);\n  }, [handleObserver]);\n\n  if (error) {\n    console.log(\"error => \", error);\n  }\n\n  return (\n    <div className=\"bg-white\">\n      <SEO title={t(\"offers\")} />\n      <BannerList data={banners} loading={isLoading} />\n      {isFetchingNextPage && <Loader />}\n      <div ref={loader} />\n    </div>\n  );\n}\n", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\ads\\[id].tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\api\\hello.ts", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\be-seller.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\blog\\index.tsx", ["1485"], [], "import Loader from \"components/loader/loader\";\nimport SEO from \"components/seo\";\nimport BlogList from \"containers/blogList/blogList\";\nimport { GetStaticProps } from \"next\";\nimport React, { useCallback, useEffect, useRef } from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport { dehydrate, QueryClient, useInfiniteQuery } from \"react-query\";\nimport blogService from \"services/blog\";\nimport getLanguage from \"utils/getLanguage\";\nimport { getCookie } from \"utils/session\";\n\nconst PER_PAGE = 10;\n\ntype Props = {};\n\nexport default function BlogPage({}: Props) {\n  const { t, i18n } = useTranslation();\n  const locale = i18n.language;\n  const loader = useRef(null);\n\n  const { data, fetchNextPage, hasNextPage, isFetchingNextPage, error } =\n    useInfiniteQuery(\n      [\"blogs\", locale],\n      ({ pageParam = 1 }) =>\n        blogService.getAll({\n          page: pageParam,\n          perPage: PER_PAGE,\n          active: 1,\n        }),\n      {\n        staleTime: 0,\n        getNextPageParam: (lastPage: any) => {\n          if (lastPage.meta.current_page < lastPage.meta.last_page) {\n            return lastPage.meta.current_page + 1;\n          }\n          return undefined;\n        },\n      }\n    );\n\n  const handleObserver = useCallback((entries: any) => {\n    const target = entries[0];\n    if (target.isIntersecting && hasNextPage) {\n      fetchNextPage();\n    }\n  }, []);\n\n  useEffect(() => {\n    const option = {\n      root: null,\n      rootMargin: \"20px\",\n      threshold: 0,\n    };\n    const observer = new IntersectionObserver(handleObserver, option);\n    if (loader.current) observer.observe(loader.current);\n  }, [handleObserver]);\n\n  if (error) {\n    console.log(\"error => \", error);\n  }\n\n  return (\n    <>\n      <SEO title={t(\"blog\")} />\n      <BlogList data={data?.pages?.flatMap((item) => item.data) || []} />\n      {isFetchingNextPage && <Loader />}\n      <div ref={loader} />\n    </>\n  );\n}\n\nexport const getStaticProps: GetStaticProps = async (ctx) => {\n  const queryClient = new QueryClient();\n  const locale = getLanguage(getCookie(\"locale\", ctx));\n\n  await queryClient.prefetchInfiniteQuery([\"blogs\", locale], () =>\n    blogService.getAll({ perPage: PER_PAGE, active: 1 })\n  );\n\n  return {\n    props: {\n      dehydratedState: JSON.parse(JSON.stringify(dehydrate(queryClient))),\n    },\n    revalidate: 3600,\n  };\n};\n", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\blog\\[id].tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\brands.tsx", ["1486"], [], "import React, { useCallback, useEffect, useRef } from \"react\";\nimport SEO from \"components/seo\";\nimport { useInfiniteQuery } from \"react-query\";\nimport useUserLocation from \"hooks/useUserLocation\";\nimport useLocale from \"hooks/useLocale\";\nimport qs from \"qs\";\nimport shopService from \"services/shop\";\nimport BrandPage from \"containers/brand/brand\";\nimport Loader from \"components/loader/loader\";\nimport Empty from \"components/empty/empty\";\n\ntype Props = {};\n\nconst PER_PAGE = 20;\n\nexport default function Brands({}: Props) {\n  const { t, locale } = useLocale();\n  const loader = useRef(null);\n  const location = useUserLocation();\n\n  const {\n    data,\n    error,\n    fetchNextPage,\n    hasNextPage,\n    isFetchingNextPage,\n    isLoading,\n  } = useInfiniteQuery(\n    [\"brands\", locale, location],\n    ({ pageParam = 1 }) =>\n      shopService.getAll(\n        qs.stringify({\n          page: pageParam,\n          perPage: PER_PAGE,\n          address: location,\n          open: 1,\n        })\n      ),\n    {\n      getNextPageParam: (lastPage: any) => {\n        if (lastPage.meta.current_page < lastPage.meta.last_page) {\n          return lastPage.meta.current_page + 1;\n        }\n        return undefined;\n      },\n    }\n  );\n  const shops = data?.pages?.flatMap((item) => item.data) || [];\n\n  const handleObserver = useCallback((entries: any) => {\n    const target = entries[0];\n    if (target.isIntersecting && hasNextPage) {\n      fetchNextPage();\n    }\n  }, []);\n\n  useEffect(() => {\n    const option = {\n      root: null,\n      rootMargin: \"20px\",\n      threshold: 0,\n    };\n    const observer = new IntersectionObserver(handleObserver, option);\n    if (loader.current) observer.observe(loader.current);\n  }, [handleObserver]);\n\n  if (error) {\n    console.log(\"error => \", error);\n  }\n\n  return (\n    <>\n      <SEO title={t(\"favorite.brands\")} />\n      <BrandPage\n        title={t(\"favorite.brands\")}\n        data={shops}\n        loading={isLoading && !isFetchingNextPage}\n      />\n      {isFetchingNextPage && <Loader />}\n      <div ref={loader} />\n      {!shops.length && !isLoading && <Empty text={t(\"no.restaurants\")} />}\n    </>\n  );\n}\n", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\careers\\index.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\careers\\[id].tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\deliver.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\group\\[id].tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\help.tsx", ["1487"], [], "import React, { useCallback, useEffect, useRef } from \"react\";\nimport SEO from \"components/seo\";\nimport HelpContainer from \"containers/help/help\";\nimport SupportCard from \"components/supportCard/supportCard\";\nimport { dehydrate, QueryClient, useInfiniteQuery } from \"react-query\";\nimport faqService from \"services/faq\";\nimport Loader from \"components/loader/loader\";\nimport { GetServerSideProps } from \"next\";\nimport { useTranslation } from \"react-i18next\";\nimport getLanguage from \"utils/getLanguage\";\n\nconst PER_PAGE = 12;\n\ntype Props = {};\n\nexport default function Help({}: Props) {\n  const { t, i18n } = useTranslation();\n  const locale = i18n.language;\n\n  const loader = useRef(null);\n\n  const { data, fetchNextPage, hasNextPage, isFetchingNextPage } =\n    useInfiniteQuery(\n      [\"faqs\", locale],\n      ({ pageParam = 1 }) =>\n        faqService.getAll({\n          page: pageParam,\n          perPage: PER_PAGE,\n        }),\n      {\n        getNextPageParam: (lastPage: any) => {\n          if (lastPage.meta.current_page < lastPage.meta.last_page) {\n            return lastPage.meta.current_page + 1;\n          }\n          return undefined;\n        },\n      }\n    );\n\n  const handleObserver = useCallback((entries: any) => {\n    const target = entries[0];\n    if (target.isIntersecting && hasNextPage) {\n      fetchNextPage();\n    }\n  }, []);\n\n  useEffect(() => {\n    const option = {\n      root: null,\n      rootMargin: \"20px\",\n      threshold: 0,\n    };\n    const observer = new IntersectionObserver(handleObserver, option);\n    if (loader.current) observer.observe(loader.current);\n  }, [handleObserver]);\n\n  return (\n    <>\n      <SEO title={t(\"help.center\")} />\n      <HelpContainer data={data?.pages?.flatMap((item) => item.data) || []}>\n        {isFetchingNextPage && <Loader />}\n        <div ref={loader} />\n        <SupportCard />\n      </HelpContainer>\n    </>\n  );\n}\n\nexport const getServerSideProps: GetServerSideProps = async ({ req }) => {\n  const queryClient = new QueryClient();\n  const locale = getLanguage(req.cookies?.locale);\n\n  await queryClient.prefetchInfiniteQuery([\"faqs\", locale], () =>\n    faqService.getAll({ perPage: PER_PAGE })\n  );\n\n  return {\n    props: {\n      dehydratedState: JSON.parse(JSON.stringify(dehydrate(queryClient))),\n    },\n  };\n};\n", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\index.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\liked.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\login.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\order-refunds.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\orders\\index.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\orders\\[id].tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\parcel-checkout.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\parcels\\index.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\parcels\\[id].tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\privacy.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\profile.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\promotion\\index.tsx", ["1488"], [], "import React, { useCallback, useEffect, useRef } from \"react\";\nimport SEO from \"components/seo\";\nimport useLocale from \"hooks/useLocale\";\nimport { useInfiniteQuery } from \"react-query\";\nimport bannerService from \"services/banner\";\nimport BannerList from \"containers/bannerList/v4\";\nimport Loader from \"components/loader/loader\";\n\ntype Props = {};\n\nexport default function Promotion({}: Props) {\n  const { t, locale } = useLocale();\n  const loader = useRef(null);\n\n  const {\n    data,\n    error,\n    fetchNextPage,\n    hasNextPage,\n    isFetchingNextPage,\n    isLoading,\n  } = useInfiniteQuery(\n    [\"bannerPaginate\", locale],\n    ({ pageParam = 1 }) =>\n      bannerService.getAll({\n        page: pageParam,\n        perPage: 10,\n      }),\n    {\n      getNextPageParam: (lastPage: any) => {\n        if (lastPage.meta.current_page < lastPage.meta.last_page) {\n          return lastPage.meta.current_page + 1;\n        }\n        return undefined;\n      },\n    }\n  );\n  const banners = data?.pages?.flatMap((item) => item.data) || [];\n\n  const handleObserver = useCallback((entries: any) => {\n    const target = entries[0];\n    if (target.isIntersecting && hasNextPage) {\n      fetchNextPage();\n    }\n  }, []);\n\n  useEffect(() => {\n    const option = {\n      root: null,\n      rootMargin: \"20px\",\n      threshold: 0,\n    };\n    const observer = new IntersectionObserver(handleObserver, option);\n    if (loader.current) observer.observe(loader.current);\n  }, [handleObserver]);\n\n  if (error) {\n    console.log(\"error => \", error);\n  }\n\n  return (\n    <div className=\"bg-white\">\n      <SEO title={t(\"offers\")} />\n      <BannerList data={banners} loading={isLoading} />\n      {isFetchingNextPage && <Loader />}\n      <div ref={loader} />\n    </div>\n  );\n}\n", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\promotion\\[id].tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\recipes\\index.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\recipes\\[id].tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\referral-terms.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\referrals.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\register.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\reservations\\index.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\reservations\\[id].tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\reset-password.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\restaurant\\[id]\\checkout.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\restaurant\\[id]\\index.tsx", [], ["1489"], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\saved-locations.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\settings\\notification.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\shop\\index.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\shop\\[id].tsx", [], ["1490", "1491"], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\shop-category\\index.tsx", ["1492"], [], "import React, { useCallback, useEffect, useRef } from \"react\";\nimport SEO from \"components/seo\";\nimport useLocale from \"hooks/useLocale\";\nimport { useInfiniteQuery } from \"react-query\";\nimport categoryService from \"services/category\";\nimport Loader from \"components/loader/loader\";\nimport CategoryList from \"containers/categoryList/categoryList\";\n\ntype Props = {};\n\nexport default function ShopCategoryPage({}: Props) {\n  const { locale } = useLocale();\n  const loader = useRef(null);\n\n  const {\n    data,\n    error,\n    fetchNextPage,\n    hasNextPage,\n    isFetchingNextPage,\n    isLoading,\n  } = useInfiniteQuery(\n    [\"shopCategoryList\", locale],\n    ({ pageParam = 1 }) =>\n      categoryService.getAllShopCategories({\n        page: pageParam,\n        perPage: 10,\n      }),\n    {\n      getNextPageParam: (lastPage: any) => {\n        if (lastPage.meta.current_page < lastPage.meta.last_page) {\n          return lastPage.meta.current_page + 1;\n        }\n        return undefined;\n      },\n    }\n  );\n  const shopCategories = data?.pages?.flatMap((item) => item.data) || [];\n\n  const handleObserver = useCallback((entries: any) => {\n    const target = entries[0];\n    if (target.isIntersecting && hasNextPage) {\n      fetchNextPage();\n    }\n  }, []);\n\n  useEffect(() => {\n    const option = {\n      root: null,\n      rootMargin: \"20px\",\n      threshold: 0,\n    };\n    const observer = new IntersectionObserver(handleObserver, option);\n    if (loader.current) observer.observe(loader.current);\n  }, [handleObserver]);\n\n  if (error) {\n    console.log(\"error => \", error);\n  }\n\n  return (\n    <>\n      <SEO />\n      <CategoryList categories={shopCategories} loading={isLoading} />\n      {isFetchingNextPage && <Loader />}\n      <div ref={loader} />\n    </>\n  );\n}\n", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\shop-category\\[id].tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\terms.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\update-details.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\update-password.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\verify-phone.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\wallet.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\welcome.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\_app.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\_document.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\addressModal\\addressCard.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\addressModal\\addressModal.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\addressModal\\deliveryAddressModal.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\addressPopover\\addressPopover.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\addressTypeSelector\\addressTypeSelector.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\adSingle\\v2.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\adSingle\\v3.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\alert\\alert.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\alert\\toast.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\appDrawer\\appDrawer.tsx", [], ["1493", "1494"], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\appDrawer\\mobileAppDrawer.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\autoRepeatOrder\\autoRepeatOrder.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\avatar.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\badge\\badge.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\badge\\v4.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\bannerHeader\\bannerHeader.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\bannerSingle\\bannerSingle.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\bannerSingle\\v2.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\beSellerModal\\beSellerModal.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\bonusCaption\\bonusCaption.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\booking\\booking.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\branchList\\branchList.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\brandShopCard\\v1.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\brandShopCard\\v4.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\button\\darkButton.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\button\\primaryButton.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\button\\secondaryButton.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\carouselArrows\\carouselArrows.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\cartButton\\cartButton.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\cartButton\\protectedCartButton.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\cartHeader\\cartHeader.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\cartHeader\\memberCartHeader.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\cartHeader\\protectedCartHeader.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\cartProduct\\cartProduct.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\cartProduct\\cartProductUI.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\cartProduct\\memberCartProduct.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\cartProduct\\protectedCartProduct.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\cartServices\\cartServices.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\cartTotal\\cartTotal.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\cartTotal\\memberCartTotal.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\categoryCard\\v1.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\categoryCard\\v4.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\categoryDropdown\\categoryDropdown.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\categorySearchInput\\categorySearchInput.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\changeAmountInput\\changeAmountInput.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\chat\\adminMessage.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\chat\\channel.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\chat\\chat.tsx", ["1495"], [], "import React, { useEffect, useRef, useState } from \"react\";\nimport {\n  <PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  MessageList,\n  MessageInput,\n} from \"@chatscope/chat-ui-kit-react\";\nimport Channel from \"./channel\";\nimport {\n  addMessage,\n  selectChat,\n  setCurrentChat,\n  setNewMessage,\n} from \"redux/slices/chat\";\nimport { createChat, sendMessage } from \"services/firebase\";\nimport { scrollTo } from \"utils/scrollTo\";\nimport { getMessages } from \"utils/getMessages\";\nimport { useTranslation } from \"react-i18next\";\nimport UploadMedia from \"./uploadMedia\";\nimport { SUPPORTED_FORMATS } from \"constants/imageFormats\";\nimport { useRouter } from \"next/router\";\nimport useModal from \"hooks/useModal\";\nimport { useAppDispatch, useAppSelector } from \"hooks/useRedux\";\nimport { useAuth } from \"contexts/auth/auth.context\";\nimport { IChat, IMessage } from \"interfaces\";\nimport { useMediaQuery } from \"@mui/material\";\nimport ModalContainer from \"containers/modal/modal\";\nimport MobileDrawer from \"containers/drawer/mobileDrawer\";\nimport { warning } from \"components/alert/toast\";\n\nexport default function Chat() {\n  const { t } = useTranslation();\n  const isDesktop = useMediaQuery(\"(min-width:1140px)\");\n  const inputRef = useRef<HTMLInputElement>(null);\n  const nextRef = useRef<HTMLInputElement>(null);\n  const { pathname, query } = useRouter();\n  const dispatch = useAppDispatch();\n  const [modal, handleOpenModal, handleCloseModal] = useModal();\n  const messageEndRef = useRef<HTMLDivElement>();\n  const [file, setFile] = useState(\"\");\n  const [url, setUrl] = useState(\"\");\n  const isShop = pathname === \"/restaurant/[id]\" || pathname === \"/shop/[id]\";\n  const isOrder = pathname === \"/orders/[id]\";\n  const shopId = String(query.id);\n  const { chats, currentChat, newMessage, roleId, messages } =\n    useAppSelector(selectChat);\n  const { user } = useAuth();\n  const groupMessages = getMessages({ currentChat, messages });\n\n  const handleChat = (myChat?: IChat) => {\n    if (user && chats) {\n      if (myChat) {\n        dispatch(setCurrentChat(myChat));\n      } else {\n        createChat({\n          shop_id: -1,\n          roleId: isShop ? shopId : isOrder ? roleId : \"admin\",\n          user: {\n            id: user.id,\n            firstname: user.firstname,\n            lastname: user.lastname,\n            img: user?.img || \"\",\n          },\n        });\n      }\n    }\n  };\n  useEffect(() => {\n    if (inputRef.current) {\n      inputRef.current.focus();\n    }\n  }, [inputRef, currentChat]);\n\n  useEffect(() => {\n    const myChat = chats\n      .filter((item) => item?.user?.id == user.id)\n      .reverse()\n      .find((item) =>\n        isShop\n          ? item.roleId == shopId\n          : isOrder\n            ? item.roleId == roleId\n            : item.roleId == \"admin\",\n      );\n    handleChat(myChat);\n  }, [chats]);\n\n  function handleFile(event: any) {\n    if (!SUPPORTED_FORMATS.includes(event.target.files[0].type)) {\n      warning(t(\"supported.image.formats.only\"));\n    } else {\n      setFile(event.target.files[0]);\n      const reader = new FileReader();\n      reader.onload = () => {\n        if (reader.readyState === 2) {\n          setUrl(String(reader.result));\n          handleOpenModal();\n        }\n      };\n      reader?.readAsDataURL(event.target.files[0]);\n    }\n  }\n\n  const handleOnChange = (value: string) => {\n    dispatch(setNewMessage(value));\n  };\n\n  const handleOnSubmit = (url: string) => {\n    const isFile = url?.includes(\"https\");\n    const trimmedMessage = newMessage\n      .replace(/\\&nbsp;/g, \"\")\n      .replace(/<[^>]+>/g, \"\")\n      .trim();\n    const payload = {\n      chat_content: trimmedMessage,\n      chat_id: currentChat?.id || 0,\n      sender: 1,\n      unread: true,\n      roleId: isShop ? shopId : isOrder ? roleId : \"admin\",\n      created_at: new Date().toString(),\n    } as IMessage;\n\n    if (isFile) payload.chat_img = url;\n    if (trimmedMessage || isFile) {\n      sendMessage(payload);\n      dispatch(setNewMessage(\"\"));\n      dispatch(addMessage({ ...payload, status: \"pending\" }));\n      const topPosition = messageEndRef.current?.offsetTop || 0;\n      const container = document.querySelector(\n        \".message-list .scrollbar-container\",\n      );\n      scrollTo(container, topPosition - 30, 600);\n      setUrl(\"\");\n      handleCloseModal();\n    }\n  };\n\n  const onAttachClick = () => {\n    nextRef.current?.click();\n  };\n\n  return (\n    <div className=\"chat-drawer\">\n      <div className=\"header\">\n        <h3 className=\"title\">{t(\"help.center\")}</h3>\n      </div>\n      <div className=\"chat-wrapper\">\n        <input\n          type=\"file\"\n          ref={nextRef}\n          onChange={handleFile}\n          accept=\"image/jpg, image/jpeg, image/png, image/svg+xml, image/svg\"\n          className=\"d-none\"\n        />\n        <MainContainer responsive className=\"chat-container rounded\">\n          <ChatContainer className=\"chat-container\">\n            <MessageList className=\"message-list\">\n              <Channel\n                groupMessages={groupMessages}\n                messageEndRef={messageEndRef}\n              />\n            </MessageList>\n            <MessageInput\n              ref={inputRef}\n              value={newMessage}\n              onChange={handleOnChange}\n              onSend={handleOnSubmit}\n              placeholder={t(\"message\")}\n              className=\"chat-input\"\n              attachButton={true}\n              onAttachClick={onAttachClick}\n            />\n          </ChatContainer>\n        </MainContainer>\n        {isDesktop ? (\n          <ModalContainer open={modal} onClose={handleCloseModal}>\n            <UploadMedia\n              url={url}\n              file={file}\n              handleOnSubmit={handleOnSubmit}\n              handleClose={handleCloseModal}\n            />\n          </ModalContainer>\n        ) : (\n          <MobileDrawer open={modal} onClose={handleCloseModal}>\n            <UploadMedia\n              url={url}\n              file={file}\n              handleOnSubmit={handleOnSubmit}\n              handleClose={handleCloseModal}\n            />\n          </MobileDrawer>\n        )}\n      </div>\n    </div>\n  );\n}\n", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\chat\\chatDate.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\chat\\rippleButton.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\chat\\uploadMedia.tsx", ["1496", "1497"], [], "import React from \"react\";\nimport TextInput from \"components/inputs/textInput\";\nimport { getDownloadURL, ref, uploadBytesResumable } from \"firebase/storage\";\nimport { setNewMessage } from \"redux/slices/chat\";\nimport { storage } from \"services/firebase\";\nimport PrimaryButton from \"components/button/primaryButton\";\nimport SecondaryButton from \"components/button/secondaryButton\";\nimport { warning } from \"components/alert/toast\";\nimport { useAppDispatch } from \"hooks/useRedux\";\nimport { useTranslation } from \"react-i18next\";\n\ntype Props = {\n  url: string;\n  setPercent?: (num: number) => void;\n  file: any;\n  handleOnSubmit: (url: string) => void;\n  handleClose: () => void;\n};\n\nexport default function UploadMedia({\n  url,\n  setPercent = (num: number) => {},\n  file,\n  handleOnSubmit,\n  handleClose,\n}: Props) {\n  const { t } = useTranslation();\n  const dispatch = useAppDispatch();\n\n  const handleUpload = () => {\n    if (!file) {\n      warning(\"Please upload an image first!\");\n    }\n    const storageRef = ref(storage, `/files/${file.name}`);\n    const uploadTask = uploadBytesResumable(storageRef, file);\n    uploadTask.on(\n      \"state_changed\",\n      (snapshot) => {\n        const percent = Math.round(\n          (snapshot.bytesTransferred / snapshot.totalBytes) * 100\n        );\n        setPercent(percent);\n        if (percent === 100) {\n        }\n      },\n      (err) => console.log(err),\n      () => {\n        getDownloadURL(uploadTask.snapshot.ref).then((url) => {\n          handleOnSubmit(url);\n        });\n      }\n    );\n  };\n\n  const handleChange = (text: string) => {\n    dispatch(setNewMessage(text));\n  };\n\n  return (\n    <div className=\"upload-media\">\n      <div className=\"upload-form\">\n        <img src={url} />\n        <div>\n          <TextInput\n            label=\"Caption\"\n            onChange={(e) => {\n              handleChange(e.target.value);\n            }}\n          />\n        </div>\n        <div className=\"footer-btns\">\n          <SecondaryButton type=\"button\" onClick={handleClose}>\n            {t(\"cancel\")}\n          </SecondaryButton>\n          <PrimaryButton type=\"button\" onClick={handleUpload}>\n            {t(\"send\")}\n          </PrimaryButton>\n        </div>\n      </div>\n    </div>\n  );\n}\n", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\chat\\userMessage.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\checkoutProductItem\\checkoutProductItem.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\clearCartModal\\cartReplacePrompt.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\clearCartModal\\clearCartModal.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\commentCard\\commentCard.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\confirmationModal\\confirmationModal.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\coupon\\coupon.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\currencyList\\currencyList.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\deliveryTimePopover\\deliveryTimePopover.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\deliveryTimes\\deliveryTimes.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\editPhone\\editPhone.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\editPhone\\insertNewPhone.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\editPhone\\newPhoneVerify.tsx", ["1498"], [], "import { error, success } from \"components/alert/toast\";\nimport PrimaryButton from \"components/button/primaryButton\";\nimport { useFormik } from \"formik\";\nimport { useTranslation } from \"react-i18next\";\nimport OtpInput from \"react-otp-input\";\nimport cls from \"./editPhone.module.scss\";\nimport { Stack } from \"@mui/material\";\nimport { useEffect } from \"react\";\nimport { useCountDown } from \"hooks/useCountDown\";\nimport { useSettings } from \"contexts/settings/settings.context\";\nimport { useAuth } from \"contexts/auth/auth.context\";\nimport profileService from \"services/profile\";\nimport dayjs from \"dayjs\";\nimport { selectCurrency } from \"redux/slices/currency\";\nimport { useAppSelector } from \"hooks/useRedux\";\nimport { useQueryClient } from \"react-query\";\n\ninterface formValues {\n  verifyId?: string;\n  verifyCode?: string;\n}\ntype Props = {\n  phone: string;\n  callback?: any;\n  setCallback?: (data: any) => void;\n  handleClose: () => void;\n};\n\nexport default function NewPhoneVerify({\n  phone,\n  callback,\n  setCallback,\n  handleClose,\n}: Props) {\n  const { t } = useTranslation();\n  const { settings } = useSettings();\n  const waitTime = settings.otp_expire_time * 60 || 60;\n  const [time, timerStart, _, timerReset] = useCountDown(waitTime);\n  const { phoneNumberSignIn, setUserData, user } = useAuth();\n  const currency = useAppSelector(selectCurrency);\n  const queryClient = useQueryClient();\n\n  const formik = useFormik({\n    initialValues: {},\n    onSubmit: (values: formValues, { setSubmitting }) => {\n      const payload = {\n        firstname: user.firstname,\n        lastname: user.lastname,\n        birthday: dayjs(user.birthday).format(\"YYYY-MM-DD\"),\n        gender: user.gender,\n        phone: parseInt(phone),\n      };\n      callback\n        .confirm(values.verifyId || \"\")\n        .then(() => {\n          profileService\n            .updatePhone(payload)\n            .then((res) => {\n              setUserData(res.data);\n              success(t(\"verified\"));\n              handleClose();\n              queryClient.invalidateQueries([\"profile\", currency?.id]);\n            })\n            .catch((err) => {\n              if (err?.data?.params?.phone) {\n                error(err?.data?.params?.phone.at(0));\n                return;\n              }\n              error(t('some.thing.went.wrong'))\n            })\n            .finally(() => setSubmitting(false));\n        })\n        .catch(() => error(t(\"verify.error\")));\n    },\n    validate: (values: formValues) => {\n      const errors: formValues = {};\n      if (!values.verifyId) {\n        errors.verifyId = t(\"required\");\n      }\n      return errors;\n    },\n  });\n\n  const handleResendCode = () => {\n    phoneNumberSignIn(phone)\n      .then((confirmationResult) => {\n        timerReset();\n        timerStart();\n        success(t(\"verify.send\"));\n        if (setCallback) setCallback(confirmationResult);\n      })\n      .catch(() => error(t(\"sms.not.sent\")));\n  };\n\n  useEffect(() => {\n    timerStart();\n  }, []);\n\n  return (\n    <form className={cls.wrapper} onSubmit={formik.handleSubmit}>\n      <div className={cls.header}>\n        <h1 className={cls.title}>{t(\"verify.phone\")}</h1>\n        <p className={cls.text}>\n          {t(\"verify.text\")} <i>{phone}</i>\n        </p>\n      </div>\n      <div className={cls.space} />\n      <Stack spacing={2}>\n        <OtpInput\n          numInputs={6}\n          inputStyle={cls.input}\n          isInputNum\n          containerStyle={cls.otpContainer}\n          value={formik.values.verifyId?.toString()}\n          onChange={(otp: any) => formik.setFieldValue(\"verifyId\", otp)}\n        />\n        <p className={cls.text}>\n          {t(\"verify.didntRecieveCode\")}{\" \"}\n          {time === 0 ? (\n            <span\n              id=\"sign-in-button\"\n              onClick={handleResendCode}\n              className={cls.resend}\n            >\n              {t(\"resend\")}\n            </span>\n          ) : (\n            <span className={cls.text}>{time} s</span>\n          )}\n        </p>\n      </Stack>\n      <div className={cls.space} />\n      <div className={cls.action}>\n        <PrimaryButton\n          type=\"submit\"\n          disabled={Number(formik?.values?.verifyId?.toString()?.length) < 6}\n          loading={formik.isSubmitting}\n        >\n          {t(\"verify\")}\n        </PrimaryButton>\n      </div>\n    </form>\n  );\n}\n", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\empty\\empty.tsx", [], ["1499"], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\emptyCart\\emptyCart.tsx", [], ["1500"], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\extrasForm\\addonsForm.tsx", [], ["1501"], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\extrasForm\\addonsItem.tsx", [], ["1502"], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\extrasForm\\extrasForm.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\fallbackImage\\fallbackImage.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\favoriteBtn\\favoriteBtn.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\favoriteBtn\\supportBtn.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\fileUpload\\fileUpload.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\filterPopover\\filterPopover.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\groupOrderButton\\groupOrderButton.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\groupOrderCard\\groupOrderCard.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\groupOrderCard\\joinGroupCard.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\icons.tsx", [], ["1503", "1504", "1505"], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\imageUpload\\imageUpload.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\checkboxInput.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\customCheckbox.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\datepicker.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\multiSelect.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\otpCodeInput.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\outlinedInput.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\passwordInput.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\phoneInputWithVerification.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\priceRangeSlider.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\radioInput.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\selectInput.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\staticDatepicker.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\switchInput.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\textArea.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\textInput.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\languagePopover\\languagePopover.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\loader\\loader.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\loader\\loading.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\loader\\pageLoading.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\loginForm\\loginForm.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\map\\map.tsx", ["1506"], ["1507", "1508"], "/* eslint-disable @next/next/no-img-element */\nimport React, { MutableRefObject, useEffect, useRef, useState } from \"react\";\nimport GoogleMapReact, { Coords } from \"google-map-react\";\nimport cls from \"./map.module.scss\";\nimport { MAP_API_KEY } from \"constants/constants\";\nimport { getAddressFromLocation } from \"utils/getAddressFromLocation\";\nimport { IShop } from \"interfaces\";\nimport ShopLogoBackground from \"components/shopLogoBackground/shopLogoBackground\";\nimport handleGooglePlacesPress from \"utils/handleGooglePlacesPress\";\nimport Price from \"components/price/price\";\n\nconst Marker = (props: any) => (\n  <div className={cls.point}>\n    <img src=\"/images/marker.png\" width={32} alt=\"Location\" />\n  </div>\n);\nconst ShopMarker = (props: any) => (\n  <div className={cls.floatCard}>\n    {props?.price && (\n      <span className={cls.price}>\n        <Price number={props.price} />\n      </span>\n    )}\n    <div className={cls.marker}>\n      <ShopLogoBackground data={props.shop} size=\"small\" />\n    </div>\n  </div>\n);\n\nconst options = {\n  fields: [\"address_components\", \"geometry\"],\n  types: [\"address\"],\n};\n\ntype Props = {\n  location: Coords;\n  setLocation?: (data: any) => void;\n  readOnly?: boolean;\n  shop?: IShop;\n  inputRef?: MutableRefObject<HTMLInputElement | null>;\n  setAddress?: (data: any) => void;\n  price?: number;\n  drawLine?: boolean;\n  defaultZoom?: number\n};\n\nexport default function Map({\n  location,\n  setLocation = () => {},\n  readOnly = false,\n  shop,\n  inputRef,\n  setAddress,\n  price,\n  drawLine,\n  defaultZoom = 15\n}: Props) {\n  const autoCompleteRef = useRef<any>();\n  const [maps, setMaps] = useState<any>();\n  const [map, setMap] = useState<any>();\n\n  async function onChangeMap(map: any) {\n    if (readOnly) {\n      return;\n    }\n    const location = {\n      lat: map.center.lat(),\n      lng: map.center.lng(),\n    };\n    setLocation(location);\n    const address = await getAddressFromLocation(\n      `${location.lat},${location.lng}`\n    );\n    if (inputRef?.current?.value) inputRef.current.value = address;\n    if (setAddress) setAddress(address);\n  }\n\n  const handleApiLoaded = (map: any, maps: any) => {\n    autoComplete(map, maps);\n    setMap(map);\n    setMaps(maps);\n    if (shop) {\n      const shopLocation = {\n        lat: Number(shop.location?.latitude) || 0,\n        lng: Number(shop.location?.longitude) || 0,\n      };\n      const markers = [location, shopLocation];\n      let bounds = new maps.LatLngBounds();\n      for (var i = 0; i < markers.length; i++) {\n        bounds.extend(markers[i]);\n      }\n      map.fitBounds(bounds);\n    }\n  };\n\n  function autoComplete(map: any, maps: any) {\n    if (inputRef) {\n      autoCompleteRef.current = new maps.places.Autocomplete(\n        inputRef.current,\n        options\n      );\n      autoCompleteRef.current.addListener(\"place_changed\", async function () {\n        const place = await autoCompleteRef.current.getPlace();\n        const address = handleGooglePlacesPress(place);\n        const coords: Coords = {\n          lat: place.geometry.location.lat(),\n          lng: place.geometry.location.lng(),\n        };\n        setLocation(coords);\n        if (setAddress) setAddress(address);\n      });\n    }\n  }\n\n  useEffect(() => {\n    if (shop && maps) {\n      const shopLocation = {\n        lat: Number(shop.location?.latitude) || 0,\n        lng: Number(shop.location?.longitude) || 0,\n      };\n      const markers = [location, shopLocation];\n      let bounds = new maps.LatLngBounds();\n      for (var i = 0; i < markers.length; i++) {\n        bounds.extend(markers[i]);\n      }\n      map.fitBounds(bounds);\n    }\n  }, [location, shop?.location, drawLine, map, maps]);\n\n  return (\n    <div className={cls.root}>\n      {!readOnly && (\n        <div className={cls.marker}>\n          <img src=\"/images/marker.png\" width={32} alt=\"Location\" />\n        </div>\n      )}\n      <GoogleMapReact\n        bootstrapURLKeys={{\n          key: MAP_API_KEY || \"\",\n          libraries: [\"places\"],\n        }}\n        zoom={defaultZoom}\n        center={location}\n        onDragEnd={onChangeMap}\n        yesIWantToUseGoogleMapApiInternals\n        onGoogleApiLoaded={({ map, maps }) => handleApiLoaded(map, maps)}\n        options={{ fullscreenControl: readOnly }}\n      >\n        {readOnly && <Marker lat={location.lat} lng={location.lng} />}\n        {!!shop && (\n          <ShopMarker\n            lat={shop.location?.latitude || 0}\n            lng={shop.location?.longitude || 0}\n            shop={shop}\n            price={price}\n          />\n        )}\n      </GoogleMapReact>\n    </div>\n  );\n}\n", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\mobileSearch\\mobileSearch.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\mobileShopCategories\\mobileProductCategories.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\mobileShopCategories\\mobileShopCategories.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\mobileShopCategories\\v2.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\notificationCenter\\notificationCenter.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\notificationCenterItem\\notificationCenterItem.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\notificationStats\\notificationStats.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\orderImage\\orderImage.tsx", ["1509"], [], "import React, { useState } from \"react\";\nimport { Order } from \"interfaces\";\nimport { useTranslation } from \"react-i18next\";\nimport ImageViewer from \"react-simple-image-viewer\";\nimport ModalContainer from \"containers/modal/modal\";\nimport cls from \"./orderImage.module.scss\";\n\ntype Props = {\n  data?: Order;\n};\n\nexport default function OrderImage({ data }: Props) {\n  const { t } = useTranslation();\n  const [isViewerOpen, setIsViewerOpen] = useState(false);\n  return (\n    <>\n      <div className={cls.wrapper}>\n        <div className={cls.header}>\n          <h3 className={cls.title}>{t(\"order.image\")}</h3>\n        </div>\n        <div className={cls.body}>\n          <img\n            src={data?.image_after_delivered}\n            alt={t(\"order.image\")}\n            onClick={() => setIsViewerOpen(true)}\n          />\n        </div>\n        <ModalContainer\n          open={isViewerOpen}\n          onClose={() => setIsViewerOpen(false)}\n        >\n          <ImageViewer\n            src={[data?.image_after_delivered || \"\"]}\n            currentIndex={0}\n            closeOnClickOutside={true}\n            onClose={() => setIsViewerOpen(false)}\n          />\n        </ModalContainer>\n      </div>\n    </>\n  );\n}\n", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\orderInfo\\orderInfo.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\orderInfo\\parcelInfo.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\orderListItem\\orderListItem.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\orderProductItem\\orderProductItem.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\orderProducts\\orderProducts.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\orderProducts\\parcelDetails.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\orderRefund\\orderRefund.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\orderReview\\orderReview.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\orderReview\\parcelReview.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\orderReview\\styledRating.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\ordersRefundButton\\ordersRefundButton.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\otp-verify\\otpVerify.tsx", ["1510"], [], "import { error, success } from \"components/alert/toast\";\nimport PrimaryButton from \"components/button/primaryButton\";\nimport { useFormik } from \"formik\";\nimport { useTranslation } from \"react-i18next\";\nimport OtpInput from \"react-otp-input\";\nimport authService from \"services/auth\";\nimport cls from \"./otpVerify.module.scss\";\nimport { Stack } from \"@mui/material\";\nimport { useEffect } from \"react\";\nimport { useMutation } from \"react-query\";\nimport { useCountDown } from \"hooks/useCountDown\";\nimport { useSettings } from \"contexts/settings/settings.context\";\nimport { useAuth } from \"contexts/auth/auth.context\";\nimport { setCookie } from \"../../utils/session\";\n\ntype RegisterViews = \"REGISTER\" | \"VERIFY\" | \"COMPLETE\";\n\ninterface formValues {\n  verifyId?: string;\n  verifyCode?: string;\n}\n\ntype Props = {\n  email: string;\n  changeView: (view: RegisterViews) => void;\n  callback?: any;\n  setCallback?: (data: any) => void;\n  verifyId?: string;\n  onSuccess?: (data: any) => void;\n};\n\nexport default function OTPVerify({\n  email,\n  changeView,\n  callback,\n  setCallback,\n  verifyId,\n  onSuccess,\n}: Props) {\n  const { t } = useTranslation();\n  const { setUserData } = useAuth();\n  const { mutate: resend, isLoading: isResending } = useMutation(\n    [\"resend\"],\n    (data: { email: string }) => authService.resendVerify(data),\n  );\n  const { settings } = useSettings();\n  const waitTime = settings.otp_expire_time * 60 || 60;\n  const [time, timerStart, _, timerReset] = useCountDown(waitTime);\n  const { phoneNumberSignIn } = useAuth();\n\n  const isUsingCustomPhoneSignIn =\n    process.env.NEXT_PUBLIC_CUSTOM_PHONE_SINGUP === \"true\";\n\n  const formik = useFormik({\n    initialValues: {},\n    onSubmit: (values: formValues, { setSubmitting }) => {\n      if (email.includes(\"@\")) {\n        authService\n          .verifyEmail(values)\n          .then(() => {\n            changeView(\"COMPLETE\");\n          })\n          .catch(() => error(t(\"verify.error\")))\n          .finally(() => setSubmitting(false));\n      } else {\n        if (isUsingCustomPhoneSignIn) {\n          authService\n            .verifyPhone({ verifyCode: values.verifyId, verifyId })\n            .then(({ data }) => {\n              const token = \"Bearer\" + \" \" + data.token;\n              setCookie(\"access_token\", token);\n              setUserData(data.user);\n              changeView(\"COMPLETE\");\n            })\n            .catch(() => error(t(\"verify.error\")))\n            .finally(() => setSubmitting(false));\n        } else {\n          callback\n            .confirm(values.verifyId || \"\")\n            .then(() => changeView(\"COMPLETE\"))\n            .catch(() => error(t(\"verify.error\")))\n            .finally(() => setSubmitting(false));\n        }\n      }\n    },\n    validate: (values: formValues) => {\n      const errors: formValues = {};\n      if (!values.verifyId) {\n        errors.verifyId = t(\"required\");\n      }\n      return errors;\n    },\n  });\n\n  const handleResendCode = () => {\n    if (email.includes(\"@\")) {\n      resend(\n        { email },\n        {\n          onSuccess: () => {\n            timerReset();\n            timerStart();\n            success(t(\"verify.send\"));\n          },\n          onError: (err: any) => {\n            error(err.message);\n          },\n        },\n      );\n    } else {\n      if (isUsingCustomPhoneSignIn) {\n        authService\n          .register({ phone: email })\n          .then((res) => {\n            onSuccess?.({\n              ...res,\n              email,\n              verifyId: res.data?.verifyId,\n            });\n            timerReset();\n            timerStart();\n            success(t(\"verify.send\"));\n          })\n          .catch(() => {\n            error(t(\"sms.not.sent\"));\n          });\n      } else {\n        phoneNumberSignIn(email)\n          .then((confirmationResult) => {\n            timerReset();\n            timerStart();\n            success(t(\"verify.send\"));\n            if (setCallback) setCallback(confirmationResult);\n          })\n          .catch(() => error(t(\"sms.not.sent\")));\n      }\n    }\n  };\n\n  useEffect(() => {\n    timerStart();\n  }, []);\n\n  return (\n    <form className={cls.wrapper} onSubmit={formik.handleSubmit}>\n      <div className={cls.header}>\n        <h1 className={cls.title}>\n          {email.includes(\"@\") ? t(\"verify.email\") : t(\"verify.phone\")}\n        </h1>\n        <p className={cls.text}>\n          {t(\"verify.text\")} <i>{email}</i>\n        </p>\n      </div>\n      <div className={cls.space} />\n      <Stack spacing={2}>\n        <OtpInput\n          numInputs={6}\n          inputStyle={cls.input}\n          isInputNum\n          containerStyle={cls.otpContainer}\n          value={formik.values.verifyId?.toString()}\n          onChange={(otp: any) => formik.setFieldValue(\"verifyId\", otp)}\n        />\n        <p className={cls.text}>\n          {t(\"verify.didntRecieveCode\")}{\" \"}\n          {time === 0 ? (\n            isResending ? (\n              <span className={cls.text} style={{ opacity: 0.5 }}>\n                Sending...\n              </span>\n            ) : (\n              <span\n                id=\"sign-in-button\"\n                onClick={handleResendCode}\n                className={cls.resend}\n              >\n                {t(\"resend\")}\n              </span>\n            )\n          ) : (\n            <span className={cls.text}>{time} s</span>\n          )}\n        </p>\n      </Stack>\n      <div className={cls.space} />\n      <div className={cls.action}>\n        <PrimaryButton\n          type=\"submit\"\n          disabled={Number(formik?.values?.verifyId?.toString()?.length) < 6}\n          loading={formik.isSubmitting}\n        >\n          {t(\"verify\")}\n        </PrimaryButton>\n      </div>\n    </form>\n  );\n}\n", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelCard\\parcelCard.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelCard\\v2.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelCard\\v3.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelFeatureContainer\\featureButtons.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelFeatureContainer\\parcelFeatureContainer.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelFeatureItem\\featureLine.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelFeatureItem\\parcelFeatureItem.tsx", ["1511"], [], "import React, { useEffect } from \"react\";\nimport cls from \"./parcelFeatureItem.module.scss\";\nimport { IParcelFeature } from \"interfaces\";\nimport CloseFillIcon from \"remixicon-react/CloseFillIcon\";\nimport Image from \"next/image\";\nimport { useTranslation } from \"react-i18next\";\nimport FeatureLine from \"./featureLine\";\nimport useTimer from \"hooks/useTimer\";\nimport { STORY_DURATION } from \"constants/story\";\nimport { useSwiper } from \"swiper/react\";\n\ntype Props = {\n  data: IParcelFeature;\n  handleClose: () => void;\n  storiesLength: number;\n  currentIndex: number;\n  storyNext: (swiper: any) => void;\n};\n\nexport default function ParcelFeatureItem({\n  data,\n  handleClose,\n  storiesLength,\n  currentIndex,\n  storyNext,\n}: Props) {\n  const { t } = useTranslation();\n  const time = useTimer(STORY_DURATION);\n  const swiper = useSwiper();\n\n  useEffect(() => {\n    if (!time) {\n      storyNext(swiper);\n    }\n  }, [time]);\n\n  return (\n    <div className={cls.story}>\n      <div className={cls.header}>\n        <div className={cls.stepper}>\n          {Array.from(new Array(storiesLength)).map((_, idx) => (\n            <FeatureLine\n              key={\"line\" + idx}\n              time={time}\n              lineIdx={idx}\n              currentIdx={currentIndex}\n              isBefore={currentIndex > idx}\n            />\n          ))}\n        </div>\n        <div className={cls.flex}>\n          <div className={cls.shop}>\n          \n          </div>\n          <button type=\"button\" className={cls.closeBtn} onClick={handleClose}>\n            <CloseFillIcon />\n          </button>\n        </div>\n      </div>\n      <Image\n        fill\n        src={data.img}\n        alt={data.title}\n        sizes=\"511px\"\n        quality={90}\n        priority\n        className={cls.storyImage}\n      />\n      <div className={cls.title}>{data.title}</div>\n    </div>\n  );\n}", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelFeatureModal\\parcelFeatureModal.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelFeaturesingle\\parcelFeatureSingle.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelForm\\parcelForm.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelForm\\parcelReceiver.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelForm\\parcelSender.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelOrderListItem\\parcleOrderListItem.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelShow\\parcelShow.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\paymentCategorySelector\\paymentCategorySelector.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\paymentMethod\\paymentMethod.tsx", ["1512"], [], "import React, { useState, useMemo } from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport RadioInput from \"components/inputs/radioInput\";\nimport cls from \"./paymentMethod.module.scss\";\nimport PrimaryButton from \"components/button/primaryButton\";\nimport { Payment } from \"interfaces\";\n\ntype PaymentCategory = \"pay_now\" | \"pay_on_delivery\";\n\ntype Props = {\n  value?: string;\n  list: Payment[];\n  handleClose: () => void;\n  onSubmit: (tag?: string) => void;\n  isButtonLoading?: boolean;\n  category?: PaymentCategory;\n};\n\nexport default function PaymentMethod({\n  value,\n  list,\n  onSubmit,\n  isButtonLoading = false,\n  category,\n}: Props) {\n  const { t } = useTranslation();\n  const [selectedValue, setSelectedValue] = useState(value);\n\n  // Define which payment methods belong to each category\n  const ONLINE_PAYMENT_METHODS = [\"mercado-pago\", \"stripe\", \"wallet\"];\n  const DELIVERY_PAYMENT_METHODS = [\"cash_delivery\", \"card_delivery\", \"pix_delivery\", \"debit_delivery\"];\n\n  // Define the desired order for delivery payment methods\n  const DELIVERY_PAYMENT_ORDER = [\"cash_delivery\", \"pix_delivery\", \"card_delivery\", \"debit_delivery\"];\n\n  // Filter and sort payment methods based on selected category\n  const filteredList = useMemo(() => {\n    if (!category) return list;\n\n    if (category === \"pay_now\") {\n      return list.filter(payment => ONLINE_PAYMENT_METHODS.includes(payment.tag));\n    } else if (category === \"pay_on_delivery\") {\n      const deliveryMethods = list.filter(payment => DELIVERY_PAYMENT_METHODS.includes(payment.tag));\n\n      // Sort delivery methods according to the specified order\n      return deliveryMethods.sort((a, b) => {\n        const indexA = DELIVERY_PAYMENT_ORDER.indexOf(a.tag);\n        const indexB = DELIVERY_PAYMENT_ORDER.indexOf(b.tag);\n\n        // If both methods are in the order array, sort by their position\n        if (indexA !== -1 && indexB !== -1) {\n          return indexA - indexB;\n        }\n\n        // If only one is in the order array, prioritize it\n        if (indexA !== -1) return -1;\n        if (indexB !== -1) return 1;\n\n        // If neither is in the order array, maintain original order\n        return 0;\n      });\n    }\n\n    return list;\n  }, [list, category]);\n\n  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {\n    setSelectedValue(event.target.value);\n    onSubmit(event.target.value);\n  };\n\n  const controlProps = (item: string) => ({\n    checked: selectedValue === item,\n    onChange: handleChange,\n    value: item,\n    id: item,\n    name: \"payment_method\",\n    inputProps: { \"aria-label\": item },\n  });\n\n  return (\n    <div className={cls.wrapper}>\n      <div className={cls.body}>\n        {filteredList.map((item) => (\n          <div key={item.id} className={cls.row}>\n            <RadioInput {...controlProps(item.tag)} />\n            <label className={cls.label} htmlFor={item.tag}>\n              <span className={cls.text}>{t(item.tag)}</span>\n            </label>\n          </div>\n        ))}\n      </div>\n      {/*<div className={cls.footer}>*/}\n      {/*  <div className={cls.action}>*/}\n      {/*    <PrimaryButton*/}\n      {/*      loading={isButtonLoading}*/}\n      {/*      onClick={() => onSubmit(selectedValue)}*/}\n      {/*    >*/}\n      {/*      {t(\"save\")}*/}\n      {/*    </PrimaryButton>*/}\n      {/*  </div>*/}\n      {/*</div>*/}\n    </div>\n  );\n}\n", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\payToUnPaidOrders\\payToUnpaidOrders.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\pickers\\rcAddressPicker.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\pickers\\rcDatePicker.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\pickers\\rcDateTimePicker.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\pickers\\rcParcelPicker.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\pickers\\rcPersonPicker.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\pickers\\rcSelect.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\pickers\\rcShopSelect.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\pickers\\rcZonePicker.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\popularBadge\\popularBadge.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\price\\price.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\productCard\\productCard.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\productGalleries\\productGalleries.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\productShare\\productShare.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\productSingle\\memberProductSingle.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\productSingle\\productSingle.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\productSingle\\productUI.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\productSingle\\protectedProductSingle.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\profileCard\\profileCard.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\profileDropdown\\profileDropdown.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\profilePassword\\profilePassword.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\recipeCard\\recipeCard.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\recipeContent\\recipeContent.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\recipeHero\\recipeHero.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\recipeIngredients\\recipeIngredients.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\recipeIngredients\\recipeStockCard.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\refundInfo\\refundInfo.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\refundListItem\\refundListItem.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\registerDetailsForm\\registerDetailsForm.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\registerForm\\registerForm.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\reservationFind\\reservationFind.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\reservationHistoryItem\\reservationHistoryItem.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\reservationTimes\\reservationTimes.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\reservationTimes\\timeSlot.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\resetPasswordForm\\resetPasswordForm.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\restaurantListForm\\asyncRestaurantListForm.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\savedLocationCard\\savedLocationCard.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\searchResult\\searchResult.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\searchResultItem\\productResultItem.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\searchResultItem\\shopResultItem.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\searchResultItem\\shopResultWithoutLink.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\searchSuggestion\\searchSuggestion.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\sendWalletMoney\\selectUsers.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\sendWalletMoney\\sendWalletMoney.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\seo.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopBanner\\shopBanner.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopCard\\shopCard.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopCard\\v2.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopCard\\v3.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopCard\\v4.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopCardDeliveryInfo\\shopCardDeliveryInfo.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopCategoryHeader\\shopCategoryHeader.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopFilter\\shopFilter.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopForm\\parcelHeaderForm.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopForm\\shopAddressForm.tsx", ["1513"], [], "import React, { useRef, useEffect } from \"react\";\nimport cls from \"./shopForm.module.scss\";\nimport { Grid } from \"@mui/material\";\nimport TextInput from \"components/inputs/textInput\";\nimport { ShopFormType } from \"interfaces\";\nimport { FormikProps } from \"formik\";\nimport { useTranslation } from \"react-i18next\";\nimport Map from \"components/map/map\";\nimport PrimaryButton from \"components/button/primaryButton\";\n\ntype Props = {\n  children?: any;\n  formik: FormikProps<ShopFormType>;\n  lang: string;\n  loading?: boolean;\n};\n\nexport default function ShopAddressForm({ formik, lang, loading }: Props) {\n  const { t } = useTranslation();\n  const { address, location } = formik.values;\n  const inputRef = useRef<HTMLInputElement>(null);\n\n  const locationObj = {\n    lat: Number(location?.split(\",\")[0]),\n    lng: Number(location?.split(\",\")[1]),\n  };\n\n  function setLocation(latlng: any) {\n    const value = `${latlng.lat},${latlng.lng}`;\n    formik.setFieldValue(\"location\", value);\n  }\n\n  function setAddress(text?: string) {\n    formik.setFieldValue(`address.${lang}`, text);\n  }\n\n  useEffect(() => {\n    setAddress(inputRef.current?.value);\n  }, [location]);\n\n  return (\n    <Grid container spacing={4}>\n      <Grid item xs={12}>\n        <TextInput\n          name={`address.${lang}`}\n          label={t(\"address\")}\n          placeholder={t(\"type.here\")}\n          defaultValue={address[lang]}\n          inputRef={inputRef}\n        />\n      </Grid>\n      <Grid item xs={12}>\n        <div className={cls.map}>\n          <Map\n            location={locationObj}\n            setLocation={setLocation}\n            inputRef={inputRef}\n          />\n        </div>\n      </Grid>\n      <Grid item xs={12} md={4} lg={3}>\n        <PrimaryButton type=\"submit\" loading={loading}>\n          {t(\"submit\")}\n        </PrimaryButton>\n      </Grid>\n    </Grid>\n  );\n}\n", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopForm\\shopDeliveryForm.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopForm\\shopForm.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopForm\\shopFormTypeTabs.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopForm\\shopGeneralForm.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopHeroCard\\shopHeroCard.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopInfoDetails\\shopInfoDetails.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopLogo\\shopLogo.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopLogoBackground\\shopLogoBackground.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopShare\\shopShare.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopSorting\\shopSorting.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\socialLogin\\socialLogin.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\stepperComponent\\parcelStepper.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\stepperComponent\\stepperComponent.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storeCard\\storeCard.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storeCard\\v2.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storeCard\\v3.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storyItem\\storyItem.tsx", ["1514"], [], "import React, { useEffect } from \"react\";\nimport cls from \"./storyItem.module.scss\";\nimport { Story } from \"interfaces\";\nimport ShopLogoBackground from \"components/shopLogoBackground/shopLogoBackground\";\nimport CloseFillIcon from \"remixicon-react/CloseFillIcon\";\nimport Image from \"next/image\";\nimport PrimaryButton from \"components/button/primaryButton\";\nimport { useTranslation } from \"react-i18next\";\nimport StoryLine from \"./storyLine\";\nimport useTimer from \"hooks/useTimer\";\nimport { STORY_DURATION } from \"constants/story\";\nimport { useSwiper } from \"swiper/react\";\nimport { useRouter } from \"next/router\";\nimport useRouterStatus from \"hooks/useRouterStatus\";\nimport getStoryImage from \"utils/getStoryImage\";\nimport dayjs from \"dayjs\";\n\ntype Props = {\n  data: Story;\n  handleClose: () => void;\n  storiesLength: number;\n  currentIndex: number;\n  storyNext: (swiper: any) => void;\n};\n\nexport default function StoryItem({\n  data,\n  handleClose,\n  storiesLength,\n  currentIndex,\n  storyNext,\n}: Props) {\n  const { t } = useTranslation();\n  const time = useTimer(STORY_DURATION);\n  const swiper = useSwiper();\n  const { push } = useRouter();\n  const { isLoading } = useRouterStatus();\n\n  useEffect(() => {\n    if (!time) {\n      storyNext(swiper);\n    }\n  }, [time]);\n\n  const goToOrder = () => {\n    push(\n      `/restaurant/${data.shop_id}?product=${data.product_uuid}`,\n      undefined,\n      { shallow: true }\n    );\n  };\n\n  return (\n    <div className={cls.story}>\n      <div className={cls.gradient} />\n      <div className={cls.header}>\n        <div className={cls.stepper}>\n          {Array.from(new Array(storiesLength)).map((_, idx) => (\n            <StoryLine\n              key={\"line\" + idx}\n              time={time}\n              lineIdx={idx}\n              currentIdx={currentIndex}\n              isBefore={currentIndex > idx}\n            />\n          ))}\n        </div>\n        <div className={cls.flex}>\n          <div className={cls.shop}>\n            <ShopLogoBackground\n              data={{\n                logo_img: data.logo_img,\n                translation: {\n                  title: data.title,\n                  locale: \"en\",\n                  description: \"\",\n                },\n                id: data.shop_id,\n                price: 0,\n                open: true,\n                verify: 0\n              }}\n              size=\"small\"\n            />\n            <p className={cls.title}>{data.title}</p>\n            <p className={cls.caption}>\n              {Math.abs(dayjs(data.created_at).diff(new Date(), \"hours\"))}{\" \"}\n              {t(\"hours.ago\")}\n            </p>\n          </div>\n          <button type=\"button\" className={cls.closeBtn} onClick={handleClose}>\n            <CloseFillIcon />\n          </button>\n        </div>\n      </div>\n      <Image\n        fill\n        src={getStoryImage(data.url)}\n        alt={data.title}\n        sizes=\"511px\"\n        quality={90}\n        priority\n        className={cls.storyImage}\n      />\n      <div className={cls.footer}>\n        <PrimaryButton onClick={goToOrder} loading={isLoading}>\n          {t(\"go.to.order\")}\n        </PrimaryButton>\n      </div>\n    </div>\n  );\n}\n", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storyItem\\storyLine.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storyItem\\storyLinev4.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storyItem\\v2.tsx", ["1515"], [], "import React, { useEffect } from \"react\";\nimport cls from \"./storyItem.module.scss\";\nimport { Story } from \"interfaces\";\nimport ShopLogoBackground from \"components/shopLogoBackground/shopLogoBackground\";\nimport CloseFillIcon from \"remixicon-react/CloseFillIcon\";\nimport Image from \"next/image\";\nimport PrimaryButton from \"components/button/primaryButton\";\nimport { useTranslation } from \"react-i18next\";\nimport StoryLine from \"./storyLine\";\nimport useTimer from \"hooks/useTimer\";\nimport { STORY_DURATION } from \"constants/story\";\nimport { useSwiper } from \"swiper/react\";\nimport { useRouter } from \"next/router\";\nimport useRouterStatus from \"hooks/useRouterStatus\";\nimport getStoryImage from \"utils/getStoryImage\";\n\ntype Props = {\n  data: Story;\n  handleClose: () => void;\n  storiesLength: number;\n  currentIndex: number;\n  storyNext: (swiper: any) => void;\n};\n\nexport default function StoryItem({\n  data,\n  handleClose,\n  storiesLength,\n  currentIndex,\n  storyNext,\n}: Props) {\n  const { t } = useTranslation();\n  const time = useTimer(STORY_DURATION);\n  const swiper = useSwiper();\n  const { push } = useRouter();\n  const { isLoading } = useRouterStatus();\n\n  useEffect(() => {\n    if (!time) {\n      storyNext(swiper);\n    }\n  }, [time]);\n\n  const goToOrder = () => {\n    push(`/shop/${data.shop_id}?product=${data.product_uuid}`, undefined, {\n      shallow: true,\n    });\n  };\n\n  return (\n    <div className={cls.story}>\n      <div className={cls.gradient} />\n      <div className={cls.header}>\n        <div className={cls.stepper}>\n          {Array.from(new Array(storiesLength)).map((_, idx) => (\n            <StoryLine\n              key={\"line\" + idx}\n              time={time}\n              lineIdx={idx}\n              currentIdx={currentIndex}\n              isBefore={currentIndex > idx}\n            />\n          ))}\n        </div>\n        <div className={cls.flex}>\n          <div className={cls.shop}>\n            <ShopLogoBackground\n              data={{\n                logo_img: data.logo_img,\n                translation: {\n                  title: data.title,\n                  locale: \"en\",\n                  description: \"\",\n                },\n                id: data.shop_id,\n                price: 0,\n                open: true,\n                verify: 0\n              }}\n              size=\"small\"\n            />\n            <p className={cls.title}>{data.title}</p>\n          </div>\n          <button type=\"button\" className={cls.closeBtn} onClick={handleClose}>\n            <CloseFillIcon />\n          </button>\n        </div>\n      </div>\n      <Image\n        fill\n        src={getStoryImage(data.url)}\n        alt={data.title}\n        sizes=\"511px\"\n        quality={90}\n        priority\n        className={cls.storyImage}\n      />\n      <div className={cls.footer}>\n        <PrimaryButton onClick={goToOrder} loading={isLoading}>\n          {t(\"go.to.order\")}\n        </PrimaryButton>\n      </div>\n    </div>\n  );\n}\n", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storyItem\\v4.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storyMenu\\storyMenu.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storyModal\\storyModal.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storyModal\\v2.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storyModal\\v4.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storySingle\\storySingle.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storySingle\\v2.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storySingle\\v3.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storySinglev4\\storySingle.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\successModal\\successModal.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\supportCard\\supportCard.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\tip\\tip.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\tip\\tipWithoutPayment.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\unauthorized\\unauthorized.tsx", [], ["1516"], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\updatePasswordForm\\updatePasswordForm.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\verifiedComponent\\verifiedComponent.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\verifyCodeForm\\verifyCodeForm.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\verifyCodeForm\\verifyPhoneCode.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\walletActionButtons\\walletActionButtons.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\walletHistoryItem\\walletHistoryItem.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\walletTopup\\walletTopup.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\welcomeBlog\\welcomeBlog.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\welcomeCard\\welcomeCard.tsx", [], ["1517", "1518"], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\welcomeFeatures\\welcomeFeatures.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\welcomeHeader\\welcomeHeader.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\welcomeHero\\welcomeHero.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\whyChooseUs\\whyChooseUs.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\zoneNotFound\\zoneNotFound.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\zoneShow\\zoneShow.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\test-final-icons.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\_error.tsx", [], [], {"ruleId": "1519", "severity": 1, "message": "1520", "line": 45, "column": 6, "nodeType": "1521", "endLine": 45, "endColumn": 8, "suggestions": "1522"}, {"ruleId": "1519", "severity": 1, "message": "1520", "line": 46, "column": 6, "nodeType": "1521", "endLine": 46, "endColumn": 8, "suggestions": "1523"}, {"ruleId": "1519", "severity": 1, "message": "1520", "line": 55, "column": 6, "nodeType": "1521", "endLine": 55, "endColumn": 8, "suggestions": "1524"}, {"ruleId": "1519", "severity": 1, "message": "1520", "line": 45, "column": 6, "nodeType": "1521", "endLine": 45, "endColumn": 8, "suggestions": "1525"}, {"ruleId": "1519", "severity": 1, "message": "1520", "line": 45, "column": 6, "nodeType": "1521", "endLine": 45, "endColumn": 8, "suggestions": "1526"}, {"ruleId": "1519", "severity": 1, "message": "1527", "line": 97, "column": 6, "nodeType": "1521", "endLine": 97, "endColumn": 27, "suggestions": "1528", "suppressions": "1529"}, {"ruleId": "1519", "severity": 1, "message": "1527", "line": 114, "column": 6, "nodeType": "1521", "endLine": 114, "endColumn": 27, "suggestions": "1530", "suppressions": "1531"}, {"ruleId": "1519", "severity": 1, "message": "1532", "line": 241, "column": 5, "nodeType": "1521", "endLine": 241, "endColumn": 26, "suggestions": "1533", "suppressions": "1534"}, {"ruleId": "1519", "severity": 1, "message": "1520", "line": 45, "column": 6, "nodeType": "1521", "endLine": 45, "endColumn": 8, "suggestions": "1535"}, {"ruleId": "1536", "severity": 1, "message": "1537", "line": 77, "column": 17, "nodeType": "1538", "endLine": 77, "endColumn": 69, "suppressions": "1539"}, {"ruleId": "1536", "severity": 1, "message": "1537", "line": 87, "column": 17, "nodeType": "1538", "endLine": 87, "endColumn": 73, "suppressions": "1540"}, {"ruleId": "1519", "severity": 1, "message": "1541", "line": 86, "column": 6, "nodeType": "1521", "endLine": 86, "endColumn": 13, "suggestions": "1542"}, {"ruleId": "1536", "severity": 1, "message": "1537", "line": 62, "column": 9, "nodeType": "1538", "endLine": 62, "endColumn": 26}, {"ruleId": "1543", "severity": 1, "message": "1544", "line": 62, "column": 9, "nodeType": "1538", "endLine": 62, "endColumn": 26}, {"ruleId": "1519", "severity": 1, "message": "1545", "line": 97, "column": 6, "nodeType": "1521", "endLine": 97, "endColumn": 8, "suggestions": "1546"}, {"ruleId": "1536", "severity": 1, "message": "1537", "line": 21, "column": 9, "nodeType": "1538", "endLine": 21, "endColumn": 61, "suppressions": "1547"}, {"ruleId": "1536", "severity": 1, "message": "1537", "line": 13, "column": 7, "nodeType": "1538", "endLine": 17, "endColumn": 9, "suppressions": "1548"}, {"ruleId": "1519", "severity": 1, "message": "1549", "line": 50, "column": 5, "nodeType": "1521", "endLine": 50, "endColumn": 21, "suggestions": "1550", "suppressions": "1551"}, {"ruleId": "1519", "severity": 1, "message": "1552", "line": 41, "column": 6, "nodeType": "1521", "endLine": 41, "endColumn": 15, "suggestions": "1553", "suppressions": "1554"}, {"ruleId": "1536", "severity": 1, "message": "1537", "line": 11, "column": 10, "nodeType": "1538", "endLine": 11, "endColumn": 80, "suppressions": "1555"}, {"ruleId": "1536", "severity": 1, "message": "1537", "line": 16, "column": 10, "nodeType": "1538", "endLine": 16, "endColumn": 90, "suppressions": "1556"}, {"ruleId": "1536", "severity": 1, "message": "1537", "line": 43, "column": 3, "nodeType": "1538", "endLine": 48, "endColumn": 5, "suppressions": "1557"}, {"ruleId": "1519", "severity": 1, "message": "1558", "line": 128, "column": 6, "nodeType": "1521", "endLine": 128, "endColumn": 53, "suggestions": "1559"}, {"ruleId": "1536", "severity": 1, "message": "1537", "line": 14, "column": 5, "nodeType": "1538", "endLine": 14, "endColumn": 63, "suppressions": "1560"}, {"ruleId": "1536", "severity": 1, "message": "1537", "line": 134, "column": 11, "nodeType": "1538", "endLine": 134, "endColumn": 69, "suppressions": "1561"}, {"ruleId": "1536", "severity": 1, "message": "1537", "line": 22, "column": 11, "nodeType": "1538", "endLine": 26, "endColumn": 13}, {"ruleId": "1519", "severity": 1, "message": "1545", "line": 142, "column": 6, "nodeType": "1521", "endLine": 142, "endColumn": 8, "suggestions": "1562"}, {"ruleId": "1519", "severity": 1, "message": "1563", "line": 35, "column": 6, "nodeType": "1521", "endLine": 35, "endColumn": 12, "suggestions": "1564"}, {"ruleId": "1519", "severity": 1, "message": "1565", "line": 65, "column": 6, "nodeType": "1521", "endLine": 65, "endColumn": 22, "suggestions": "1566"}, {"ruleId": "1519", "severity": 1, "message": "1567", "line": 39, "column": 6, "nodeType": "1521", "endLine": 39, "endColumn": 16, "suggestions": "1568"}, {"ruleId": "1519", "severity": 1, "message": "1563", "line": 43, "column": 6, "nodeType": "1521", "endLine": 43, "endColumn": 12, "suggestions": "1569"}, {"ruleId": "1519", "severity": 1, "message": "1563", "line": 42, "column": 6, "nodeType": "1521", "endLine": 42, "endColumn": 12, "suggestions": "1570"}, {"ruleId": "1536", "severity": 1, "message": "1537", "line": 18, "column": 7, "nodeType": "1538", "endLine": 18, "endColumn": 66, "suppressions": "1571"}, {"ruleId": "1536", "severity": 1, "message": "1537", "line": 29, "column": 15, "nodeType": "1538", "endLine": 29, "endColumn": 67, "suppressions": "1572"}, {"ruleId": "1536", "severity": 1, "message": "1537", "line": 39, "column": 15, "nodeType": "1538", "endLine": 39, "endColumn": 71, "suppressions": "1573"}, "react-hooks/exhaustive-deps", "React Hook useCallback has missing dependencies: 'fetchNextPage' and 'hasNextPage'. Either include them or remove the dependency array.", "ArrayExpression", ["1574"], ["1575"], ["1576"], ["1577"], ["1578"], "React Hook useEffect has missing dependencies: 'handleSearch', 'isSearchCategorySearchOpen', and 'products?.data?.all'. Either include them or remove the dependency array.", ["1579"], ["1580"], ["1581"], ["1582"], "React Hook useCallback has missing dependencies: 'isBigDesktop' and 'isDesktop'. Either include them or remove the dependency array.", ["1583"], ["1584"], ["1585"], "@next/next/no-img-element", "Do not use `<img>` element. Use `<Image />` from `next/image` instead. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", ["1586"], ["1587"], "React Hook useEffect has missing dependencies: 'handleChat', 'isOrder', 'isShop', 'roleId', 'shopId', and 'user.id'. Either include them or remove the dependency array.", ["1588"], "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "React Hook useEffect has a missing dependency: 'timerStart'. Either include it or remove the dependency array.", ["1589"], ["1590"], ["1591"], "React Hook useCallback has a missing dependency: 'onSelectAddon'. Either include it or remove the dependency array. If 'onSelectAddon' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1592"], ["1593"], "React Hook useEffect has missing dependencies: 'data' and 'handleChange'. Either include them or remove the dependency array. If 'handleChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1594"], ["1595"], ["1596"], ["1597"], ["1598"], "React Hook useEffect has a missing dependency: 'shop'. Either include it or remove the dependency array.", ["1599"], ["1600"], ["1601"], ["1602"], "React Hook useEffect has missing dependencies: 'storyNext' and 'swiper'. Either include them or remove the dependency array. If 'storyNext' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1603"], "React Hook useMemo has missing dependencies: 'DELIVERY_PAYMENT_METHODS', 'DELIVERY_PAYMENT_ORDER', and 'ONLINE_PAYMENT_METHODS'. Either include them or remove the dependency array.", ["1604"], "React Hook useEffect has a missing dependency: 'setAddress'. Either include it or remove the dependency array.", ["1605"], ["1606"], ["1607"], ["1608"], ["1609"], ["1610"], {"desc": "1611", "fix": "1612"}, {"desc": "1611", "fix": "1613"}, {"desc": "1611", "fix": "1614"}, {"desc": "1611", "fix": "1615"}, {"desc": "1611", "fix": "1616"}, {"desc": "1617", "fix": "1618"}, {"kind": "1619", "justification": "1620"}, {"desc": "1617", "fix": "1621"}, {"kind": "1619", "justification": "1620"}, {"desc": "1622", "fix": "1623"}, {"kind": "1619", "justification": "1620"}, {"desc": "1611", "fix": "1624"}, {"kind": "1619", "justification": "1620"}, {"kind": "1619", "justification": "1620"}, {"desc": "1625", "fix": "1626"}, {"desc": "1627", "fix": "1628"}, {"kind": "1619", "justification": "1620"}, {"kind": "1619", "justification": "1620"}, {"desc": "1629", "fix": "1630"}, {"kind": "1619", "justification": "1620"}, {"desc": "1631", "fix": "1632"}, {"kind": "1619", "justification": "1620"}, {"kind": "1619", "justification": "1620"}, {"kind": "1619", "justification": "1620"}, {"kind": "1619", "justification": "1620"}, {"desc": "1633", "fix": "1634"}, {"kind": "1619", "justification": "1620"}, {"kind": "1619", "justification": "1620"}, {"desc": "1627", "fix": "1635"}, {"desc": "1636", "fix": "1637"}, {"desc": "1638", "fix": "1639"}, {"desc": "1640", "fix": "1641"}, {"desc": "1636", "fix": "1642"}, {"desc": "1636", "fix": "1643"}, {"kind": "1619", "justification": "1620"}, {"kind": "1619", "justification": "1620"}, {"kind": "1619", "justification": "1620"}, "Update the dependencies array to be: [fetchNextPage, hasNextPage]", {"range": "1644", "text": "1645"}, {"range": "1646", "text": "1645"}, {"range": "1647", "text": "1645"}, {"range": "1648", "text": "1645"}, {"range": "1649", "text": "1645"}, "Update the dependencies array to be: [debounce<PERSON>ear<PERSON><PERSON><PERSON><PERSON>, handleSearch, isSearchCategorySearchOpen, products?.data?.all]", {"range": "1650", "text": "1651"}, "directive", "", {"range": "1652", "text": "1651"}, "Update the dependencies array to be: [isBigDesktop, isDesktop, products?.data?.all]", {"range": "1653", "text": "1654"}, {"range": "1655", "text": "1645"}, "Update the dependencies array to be: [chats, handleChat, isOrder, isShop, roleId, shopId, user.id]", {"range": "1656", "text": "1657"}, "Update the dependencies array to be: [timerStart]", {"range": "1658", "text": "1659"}, "Update the dependencies array to be: [onSelectAddon, selectedAddons]", {"range": "1660", "text": "1661"}, "Update the dependencies array to be: [counter, data, handleChange]", {"range": "1662", "text": "1663"}, "Update the dependencies array to be: [location, shop.location, drawLine, map, maps, shop]", {"range": "1664", "text": "1665"}, {"range": "1666", "text": "1659"}, "Update the dependencies array to be: [storyNext, swiper, time]", {"range": "1667", "text": "1668"}, "Update the dependencies array to be: [category, list, ONLINE_PAYMENT_METHODS, DELIVERY_PAYMENT_METHODS, DELIVERY_PAYMENT_ORDER]", {"range": "1669", "text": "1670"}, "Update the dependencies array to be: [location, setAddress]", {"range": "1671", "text": "1672"}, {"range": "1673", "text": "1668"}, {"range": "1674", "text": "1668"}, [1194, 1196], "[fetchNextPage, hasNextPage]", [1379, 1381], [1456, 1458], [1342, 1344], [1200, 1202], [3376, 3397], "[debounce<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, handleSearch, isSearchCategorySearchOpen, products?.data?.all]", [3868, 3889], [7327, 7348], "[isBigDesktop, isDesktop, products?.data?.all]", [1247, 1249], [2796, 2803], "[chats, handleChat, isOrder, isShop, roleId, shopId, user.id]", [2971, 2973], "[timerStart]", [1332, 1348], "[on<PERSON><PERSON>ct<PERSON><PERSON><PERSON>, selectedAdd<PERSON>]", [1066, 1075], "[counter, data, handleChange]", [3605, 3652], "[location, shop.location, drawLine, map, maps, shop]", [4075, 4077], [909, 915], "[story<PERSON><PERSON><PERSON>, swiper, time]", [2214, 2230], "[category, list, ONLINE_PAYMENT_METHODS, DELIVERY_PAYMENT_METHODS, DELIVERY_PAYMENT_ORDER]", [1144, 1154], "[location, setAddress]", [1260, 1266], [1233, 1239]]