import dayjs from "dayjs";
import "dayjs/locale/pt-br";

/**
 * Formats a date in Brazilian Portuguese format
 * @param date - Date string or Date object to format
 * @param includeTime - Whether to include time in the format (default: true)
 * @returns Formatted date string like "Sex, 25 de jul, 02:30" or "Sex, 25 de jul"
 */
export function formatBrazilianDate(date: string | Date, includeTime: boolean = true): string {
  if (!date) return "";
  
  const dayjsDate = dayjs(date).locale('pt-br');
  
  if (includeTime) {
    // Format: "Sex, 25 de jul, 02:30"
    return dayjsDate.format("ddd, DD [de] MMM, HH:mm");
  } else {
    // Format: "Sex, 25 de jul"
    return dayjsDate.format("ddd, DD [de] MMM");
  }
}

/**
 * Formats a date with time in Brazilian Portuguese format
 * @param date - Date string or Date object to format
 * @param time - Time string to append (optional)
 * @returns Formatted date string like "Sex, 25 de jul, 02:30"
 */
export function formatBrazilianDateTime(date: string | Date, time?: string): string {
  if (!date) return "";
  
  const dayjsDate = dayjs(date).locale('pt-br');
  
  if (time) {
    // Format: "Sex, 25 de jul, 02:30" (using provided time)
    return `${dayjsDate.format("ddd, DD [de] MMM")}, ${time}`;
  } else {
    // Format: "Sex, 25 de jul, 02:30" (using date's time)
    return dayjsDate.format("ddd, DD [de] MMM, HH:mm");
  }
}

/**
 * Formats a date for order creation display
 * @param date - Date string or Date object to format
 * @returns Formatted date string like "25 de jul, 02:30"
 */
export function formatBrazilianOrderDate(date: string | Date): string {
  if (!date) return "";
  
  const dayjsDate = dayjs(date).locale('pt-br');
  return dayjsDate.format("DD [de] MMM, HH:mm");
}
