"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_error";
exports.ids = ["pages/_error"];
exports.modules = {

/***/ "./pages/_error.tsx":
/*!**************************!*\
  !*** ./pages/_error.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"react-i18next\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_i18next__WEBPACK_IMPORTED_MODULE_2__]);\nreact_i18next__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nfunction Error({ statusCode , hasGetInitialPropsRun , err  }) {\n    const { t  } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    // Handle specific error cases\n    const getErrorMessage = ()=>{\n        if (statusCode === 404) {\n            return t(\"page_not_found\") || \"P\\xe1gina n\\xe3o encontrada\";\n        }\n        if (statusCode === 500) {\n            return t(\"server_error\") || \"Erro interno do servidor\";\n        }\n        if (err?.message?.includes(\"Loading chunk\")) {\n            return \"Erro ao carregar recursos. Recarregue a p\\xe1gina.\";\n        }\n        return t(\"something_went_wrong\") || \"Algo deu errado\";\n    };\n    const handleReload = ()=>{\n        if (false) {}\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\",\n            flexDirection: \"column\",\n            alignItems: \"center\",\n            justifyContent: \"center\",\n            minHeight: \"100vh\",\n            padding: \"20px\",\n            textAlign: \"center\",\n            fontFamily: \"system-ui, -apple-system, sans-serif\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                style: {\n                    fontSize: \"2rem\",\n                    marginBottom: \"1rem\",\n                    color: \"#333\"\n                },\n                children: statusCode ? `Erro ${statusCode}` : \"Erro do Cliente\"\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\pages\\\\_error.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                style: {\n                    fontSize: \"1.1rem\",\n                    marginBottom: \"2rem\",\n                    color: \"#666\"\n                },\n                children: getErrorMessage()\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\pages\\\\_error.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: handleReload,\n                style: {\n                    padding: \"12px 24px\",\n                    fontSize: \"1rem\",\n                    backgroundColor: \"#007bff\",\n                    color: \"white\",\n                    border: \"none\",\n                    borderRadius: \"6px\",\n                    cursor: \"pointer\",\n                    transition: \"background-color 0.2s\"\n                },\n                onMouseOver: (e)=>e.currentTarget.style.backgroundColor = \"#0056b3\",\n                onMouseOut: (e)=>e.currentTarget.style.backgroundColor = \"#007bff\",\n                children: \"Recarregar P\\xe1gina\"\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\pages\\\\_error.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\pages\\\\_error.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\nError.getInitialProps = ({ res , err  })=>{\n    const statusCode = res ? res.statusCode : err ? err.statusCode : 404;\n    return {\n        statusCode\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Error);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_error.tsx\n");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react-i18next":
/*!********************************!*\
  !*** external "react-i18next" ***!
  \********************************/
/***/ ((module) => {

module.exports = import("react-i18next");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("./pages/_error.tsx"));
module.exports = __webpack_exports__;

})();